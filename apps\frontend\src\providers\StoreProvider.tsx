'use client';

import { ReactNode, useRef } from 'react';
import { Provider } from 'react-redux';
import { makeStore, type AppStore } from '@/store';
import { IAuthService } from '@/auth';

interface StoreProviderProps {
  children: ReactNode;
  authService?: IAuthService;
}

export function StoreProvider({ children }: StoreProviderProps) {
  // Create store once and keep reference to prevent recreation on re-renders
  const storeRef = useRef<AppStore | undefined>(undefined);
  
  if (!storeRef.current) {
    // Create the store instance the first time this renders
    storeRef.current = makeStore();
  }

  return <Provider store={storeRef.current}>{children}</Provider>;
}

// Higher-order component for easier integration
export function withStoreProvider<P extends object>(
  Component: React.ComponentType<P>,
  authService?: IAuthService
) {
  return function StoreProviderWrapper(props: P) {
    return (
      <StoreProvider authService={authService}>
        <Component {...props} />
      </StoreProvider>
    );
  };
}

// Utility function to create store with auth service
export function createStoreProvider(authService?: IAuthService) {
  return function ConfiguredStoreProvider({ children }: { children: ReactNode }) {
    return (
      <StoreProvider authService={authService}>
        {children}
      </StoreProvider>
    );
  };
} 