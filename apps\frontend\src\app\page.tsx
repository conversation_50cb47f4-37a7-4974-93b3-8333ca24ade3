import Link from 'next/link';

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gray-100 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-gray-900 mb-8">
          PulsePanel Dashboard
        </h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">
            🚀 Auth State Migration Status
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h3 className="text-lg font-medium text-green-800 mb-2">✅ Completed</h3>
              <ul className="text-sm text-green-700 space-y-1">
                <li>• Redux Store Configuration</li>
                <li>• Auth & UI Slices</li>
                <li>• AuthServiceProvider Migration</li>
                <li>• Feature Hooks API</li>
                <li>• Memoized Selectors</li>
              </ul>
            </div>
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="text-lg font-medium text-blue-800 mb-2">🔄 In Progress</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Auth State Testing</li>
                <li>• Component Migration</li>
                <li>• Backward Compatibility</li>
              </ul>
            </div>
          </div>
          
          <div className="flex space-x-4">
            <Link 
              href="/redux-test" 
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Test Redux Features
            </Link>
            <Link 
              href="/auth-test-redux" 
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              Test Auth Migration
            </Link>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Quick Stats</h3>
            <p className="text-gray-600">Auth service integration working</p>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Features</h3>
            <p className="text-gray-600">Redux + Apollo hybrid state</p>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Performance</h3>
            <p className="text-gray-600">Optimized selectors active</p>
          </div>
        </div>
      </div>
    </div>
  );
}
