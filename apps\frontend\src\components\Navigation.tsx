"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useSidebar, useTheme, useAuth } from "@/hooks/features";
import styles from "./Navigation.module.css";

export default function Navigation() {
  const pathname = usePathname();
  
  // Feature hooks demo
  const sidebar = useSidebar();
  const theme = useTheme();
  const auth = useAuth();

  const routes = [
    { name: "Home", path: "/" },
    { name: "Integrations", path: "/integrations" },
    { name: "Redux Test", path: "/redux-test" },
    { name: "Auth Redux Test", path: "/auth-test-redux" },
  ];

  return (
    <nav className={styles.nav}>
      <div className={styles.container}>
        <div className={styles.logo}>PulsePanel</div>
        <div className={styles.menu}>
          {routes.map((route) => (
            <Link
              key={route.path}
              href={route.path}
              className={
                pathname === route.path ? styles.activeLink : styles.link
              }
            >
              {route.name}
            </Link>
          ))}
          
          {/* Redux Demo with Feature Hooks */}
          <button 
            onClick={() => sidebar.toggle()}
            style={{ 
              marginLeft: '1rem', 
              padding: '0.5rem', 
              border: '1px solid #ccc',
              borderRadius: '4px',
              background: sidebar.open ? '#e0f2fe' : '#f5f5f5'
            }}
          >
            🧪 Redux: {sidebar.open ? 'Open' : 'Closed'} | {theme.theme} | Auth: {auth.isAuthenticated ? '✅' : '❌'}
          </button>
        </div>
      </div>
    </nav>
  );
}
