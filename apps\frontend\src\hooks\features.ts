import { useCallback } from 'react';
import { useAppDispatch, useAppSelector } from './store';

// Auth feature hooks
import { 
  selectAuthStatus, 
  selectUser, 
  selectLoadingStates as selectAuthLoadingStates,
  selectErrors as selectAuthErrors,
  selectSessionInfo,
  selectHasRole,
  selectIsExpired,
  checkAuthStatus,
  signIn,
  signUp,
  signOut,
  refreshToken,
  clearAuthError,
  setCheckingAuth,
} from '@/features/auth/authSlice';

// UI feature hooks
import {
  selectSidebarState,
  selectModalState,
  selectTheme,
  selectLoadingState as selectUILoadingState,
  selectNotifications,
  selectPageInfo,
  toggleSidebar,
  setSidebarOpen,
  toggleSidebarCollapse,
  openModal,
  closeModal,
  setTheme,
  setGlobalLoading,
  addNotification,
  removeNotification,
  clearNotifications,
  setPageTitle,
  setBreadcrumbs,
  type UIState,
} from '@/features/ui/uiSlice';

import type { SignInInput, SignUpInput } from '@/auth';

// =============================================================================
// AUTH HOOKS
// =============================================================================

export function useAuth() {
  const authStatus = useAppSelector(selectAuthStatus);
  const user = useAppSelector(selectUser);
  const loadingStates = useAppSelector(selectAuthLoadingStates);
  const errors = useAppSelector(selectAuthErrors);
  const sessionInfo = useAppSelector(selectSessionInfo);
  
  return {
    ...authStatus,
    user,
    ...loadingStates,
    ...errors,
    sessionInfo,
  };
}

export function useAuthActions() {
  const dispatch = useAppDispatch();
  
  return {
    checkAuthStatus: useCallback(() => dispatch(checkAuthStatus()), [dispatch]),
    signIn: useCallback((credentials: SignInInput) => dispatch(signIn(credentials)), [dispatch]),
    signUp: useCallback((userData: SignUpInput) => dispatch(signUp(userData)), [dispatch]),
    signOut: useCallback(() => dispatch(signOut()), [dispatch]),
    refreshToken: useCallback(() => dispatch(refreshToken()), [dispatch]),
    clearAuthError: useCallback(() => dispatch(clearAuthError()), [dispatch]),
    setCheckingAuth: useCallback((checking: boolean) => dispatch(setCheckingAuth(checking)), [dispatch]),
  };
}

export function useUserRole() {
  const hasRole = useAppSelector(selectHasRole);
  const sessionInfo = useAppSelector(selectSessionInfo);
  const isExpired = useAppSelector(selectIsExpired);
  
  return {
    hasRole,
    roles: sessionInfo.roles,
    isExpired,
    checkRole: hasRole,
  };
}

// =============================================================================
// UI HOOKS
// =============================================================================

export function useSidebar() {
  const sidebarState = useAppSelector(selectSidebarState);
  const dispatch = useAppDispatch();
  
  return {
    ...sidebarState,
    toggle: useCallback(() => dispatch(toggleSidebar()), [dispatch]),
    setOpen: useCallback((open: boolean) => dispatch(setSidebarOpen(open)), [dispatch]),
    toggleCollapse: useCallback(() => dispatch(toggleSidebarCollapse()), [dispatch]),
  };
}

export function useModal() {
  const modalState = useAppSelector(selectModalState);
  const dispatch = useAppDispatch();
  
  return {
    ...modalState,
    open: useCallback((modalId: string, data?: any) => 
      dispatch(openModal({ modalId, data })), [dispatch]),
    close: useCallback(() => dispatch(closeModal()), [dispatch]),
  };
}

export function useTheme() {
  const theme = useAppSelector(selectTheme);
  const dispatch = useAppDispatch();
  
  return {
    theme,
    setTheme: useCallback((newTheme: UIState['theme']) => 
      dispatch(setTheme(newTheme)), [dispatch]),
  };
}

export function useGlobalLoading() {
  const loadingState = useAppSelector(selectUILoadingState);
  const dispatch = useAppDispatch();
  
  return {
    ...loadingState,
    setLoading: useCallback((loading: boolean, message?: string) => 
      dispatch(setGlobalLoading({ loading, message })), [dispatch]),
  };
}

export function useNotifications() {
  const notifications = useAppSelector(selectNotifications);
  const dispatch = useAppDispatch();
  
  return {
    notifications,
    add: useCallback((notification: Omit<UIState['notifications'][0], 'id'>) => 
      dispatch(addNotification(notification)), [dispatch]),
    remove: useCallback((id: string) => dispatch(removeNotification(id)), [dispatch]),
    clear: useCallback(() => dispatch(clearNotifications()), [dispatch]),
  };
}

export function usePageInfo() {
  const pageInfo = useAppSelector(selectPageInfo);
  const dispatch = useAppDispatch();
  
  return {
    ...pageInfo,
    setTitle: useCallback((title: string) => dispatch(setPageTitle(title)), [dispatch]),
    setBreadcrumbs: useCallback((breadcrumbs: UIState['breadcrumbs']) => 
      dispatch(setBreadcrumbs(breadcrumbs)), [dispatch]),
  };
}

// =============================================================================
// COMBINED HOOKS
// =============================================================================

// Hook that combines auth status with UI loading for comprehensive app state
export function useAppState() {
  const auth = useAuth();
  const loading = useGlobalLoading();
  const notifications = useNotifications();
  
  return {
    auth,
    loading,
    notifications,
    isReady: auth.isInitialized && !auth.isCheckingAuth,
  };
} 