/**
 * Enterprise Cookie Configuration for BFF-Frontend Integration
 *
 * This module provides secure cookie management with production-ready policies
 * including remember me functionality and comprehensive security controls.
 */

import type { CookieOptions } from "express";

/**
 * Cookie TTL Constants (in seconds)
 */
export const COOKIE_TTL = {
  DEFAULT: 15 * 60, // 15 minutes for normal sessions
  REMEMBER_ME: 30 * 24 * 60 * 60, // 30 days for remember me
  REFRESH: 7 * 24 * 60 * 60, // 7 days for refresh tokens
} as const;

/**
 * Session Duration Configuration
 */
export interface SessionDurationConfig {
  accessTokenValidity: number; // SuperTokens access token TTL
  refreshTokenValidity: number; // SuperTokens refresh token TTL
  sessionTTL: number; // Redis cache TTL
  cookieMaxAge: number; // Browser cookie max age
}

/**
 * Get session duration configuration based on remember me preference
 */
export function getSessionDuration(
  rememberMe: boolean = false,
): SessionDurationConfig {
  if (rememberMe) {
    return {
      accessTokenValidity: 60 * 60, // 1 hour (shorter for security)
      refreshTokenValidity: COOKIE_TTL.REMEMBER_ME, // 30 days
      sessionTTL: 30 * 60, // 30 minutes Redis cache
      cookieMaxAge: COOKIE_TTL.REMEMBER_ME * 1000, // Browser cookie (ms)
    };
  }

  return {
    accessTokenValidity: 15 * 60, // 15 minutes
    refreshTokenValidity: COOKIE_TTL.REFRESH, // 7 days
    sessionTTL: COOKIE_TTL.DEFAULT, // 15 minutes Redis cache
    cookieMaxAge: COOKIE_TTL.DEFAULT * 1000, // Browser cookie (ms)
  };
}

/**
 * Production Cookie Security Configuration
 */
export function getSecureCookieOptions(
  rememberMe: boolean = false,
): Partial<CookieOptions> {
  const isProduction = process.env.NODE_ENV === "production";
  const duration = getSessionDuration(rememberMe);

  return {
    httpOnly: true, // Prevent XSS attacks
    secure: isProduction, // HTTPS only in production
    sameSite: isProduction ? "none" : "lax", // CSRF protection
    maxAge: duration.cookieMaxAge,
    domain: isProduction ? process.env.COOKIE_DOMAIN : undefined,
    path: "/", // Available for all routes
  };
}

/**
 * SuperTokens Session Configuration
 */
export function getSuperTokensSessionConfig(rememberMe: boolean = false) {
  const duration = getSessionDuration(rememberMe);
  const isProduction = process.env.NODE_ENV === "production";

  return {
    sessionExpiredStatusCode: 401,
    accessTokenValidity: duration.accessTokenValidity * 1000, // SuperTokens expects ms
    refreshTokenValidity: duration.refreshTokenValidity * 1000, // SuperTokens expects ms
    antiCsrf: "VIA_TOKEN",
    cookieSecure: isProduction,
    cookieSameSite: isProduction ? "none" : "lax",
    cookieDomain: isProduction ? process.env.COOKIE_DOMAIN : undefined,
  };
}

/**
 * Cookie Headers for Manual Response Setting
 * Used when we need to set cookies manually in GraphQL responses
 */
export function buildCookieHeaders(
  sessionData: { accessToken?: string; refreshToken?: string },
  rememberMe: boolean = false,
): Record<string, string> {
  const cookieOptions = getSecureCookieOptions(rememberMe);
  const headers: Record<string, string> = {};

  // Build cookie option string
  const cookieAttrs = [
    cookieOptions.httpOnly ? "HttpOnly" : "",
    cookieOptions.secure ? "Secure" : "",
    cookieOptions.sameSite ? `SameSite=${cookieOptions.sameSite}` : "",
    cookieOptions.maxAge
      ? `Max-Age=${Math.floor(cookieOptions.maxAge / 1000)}`
      : "",
    cookieOptions.domain ? `Domain=${cookieOptions.domain}` : "",
    cookieOptions.path ? `Path=${cookieOptions.path}` : "",
  ]
    .filter(Boolean)
    .join("; ");

  // Access token cookie
  if (sessionData.accessToken) {
    headers["Set-Cookie-Access"] =
      `sAccessToken=${sessionData.accessToken}; ${cookieAttrs}`;
  }

  // Refresh token cookie
  if (sessionData.refreshToken) {
    headers["Set-Cookie-Refresh"] =
      `sRefreshToken=${sessionData.refreshToken}; ${cookieAttrs}`;
  }

  return headers;
}

/**
 * Clear cookie headers for logout
 */
export function buildLogoutCookieHeaders(): Record<string, string> {
  const isProduction = process.env.NODE_ENV === "production";

  const clearAttrs = [
    "HttpOnly",
    isProduction ? "Secure" : "",
    `SameSite=${isProduction ? "none" : "lax"}`,
    "Max-Age=0", // Expire immediately
    "Path=/",
    isProduction && process.env.COOKIE_DOMAIN
      ? `Domain=${process.env.COOKIE_DOMAIN}`
      : "",
  ]
    .filter(Boolean)
    .join("; ");

  return {
    "Set-Cookie-Access": `sAccessToken=; ${clearAttrs}`,
    "Set-Cookie-Refresh": `sRefreshToken=; ${clearAttrs}`,
    "Set-Cookie-IdRefresh": `sIdRefreshToken=; ${clearAttrs}`,
  };
}

/**
 * Validate cookie configuration
 */
export function validateCookieConfig(): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  const isProduction = process.env.NODE_ENV === "production";

  if (isProduction) {
    if (!process.env.COOKIE_DOMAIN) {
      errors.push(
        "COOKIE_DOMAIN environment variable is required in production",
      );
    }

    // Validate domain format
    if (
      process.env.COOKIE_DOMAIN &&
      !process.env.COOKIE_DOMAIN.startsWith(".")
    ) {
      errors.push(
        "COOKIE_DOMAIN should start with a dot (e.g., .yourdomain.com) for subdomain support",
      );
    }
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Development/Debug helpers
 */
export function getCookieDebugInfo(rememberMe: boolean = false) {
  const duration = getSessionDuration(rememberMe);
  const options = getSecureCookieOptions(rememberMe);

  return {
    environment: process.env.NODE_ENV,
    rememberMe,
    duration: {
      accessTokenTTL: `${duration.accessTokenValidity / 60} minutes`,
      refreshTokenTTL: `${duration.refreshTokenValidity / (24 * 60 * 60)} days`,
      cookieMaxAge: `${duration.cookieMaxAge / (1000 * 60)} minutes`,
      redisTTL: `${duration.sessionTTL / 60} minutes`,
    },
    security: {
      httpOnly: options.httpOnly,
      secure: options.secure,
      sameSite: options.sameSite,
      domain: options.domain || "localhost",
    },
    validation: validateCookieConfig(),
  };
}
