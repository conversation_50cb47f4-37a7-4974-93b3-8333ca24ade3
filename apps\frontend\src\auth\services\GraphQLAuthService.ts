/**
 * GraphQL Auth Service Implementation
 *
 * This service implements the IAuthService interface using GraphQL as the transport layer.
 * It provides a clean, transport-agnostic API for auth operations while internally
 * using the GraphQLTransportAdapter.
 *
 * Key Features:
 * - Transport abstraction: UI doesn't know about GraphQL details
 * - Error transformation: GraphQL errors → standardized auth errors
 * - Session state management: Caching and persistence
 * - Tenant context handling: Multi-tenant operations
 * - Performance optimization: Caching frequently accessed data
 * - Future-proof: Easy to replace with KeyCloak/Auth0 service
 */

import type {
  IAuthService,
  SignInInput,
  SignUpInput,
  AuthResult,
  User,
  SessionInfo,
  AuthConfig,
} from "@/auth";

import type { IGraphQLTransportAdapter } from "@/auth";
import { GraphQLTransportAdapter } from "@/auth";

/**
 * In-memory cache for auth data
 * In production, this could be replaced with Redis or localStorage
 */
interface AuthCache {
  user?: User | null;
  sessionInfo?: SessionInfo | null;

  isAuthenticated?: boolean;
  lastUpdate: number;
  ttl: number; // Time to live in milliseconds
}

export class GraphQLAuthService implements IAuthService {
  private transport: IGraphQLTransportAdapter;
  private cache: AuthCache = {
    lastUpdate: 0,
    ttl: 5 * 60 * 1000, // 5 minutes default TTL
  };

  constructor(
    private config: AuthConfig,
    transportAdapter?: IGraphQLTransportAdapter,
  ) {
    this.transport =
      transportAdapter ||
      new GraphQLTransportAdapter(
        config.graphqlEndpoint || `${config.apiUrl}/graphql`,
        {
          enableCaching: config.enableCaching,
          debugMode: config.debugMode,
        },
      );
  }

  /**
   * Check if cached data is still valid
   */
  private isCacheValid(): boolean {
    const now = Date.now();
    return now - this.cache.lastUpdate < this.cache.ttl;
  }

  /**
   * Update cache with new data
   */
  private updateCache(data: Partial<AuthCache>): void {
    this.cache = {
      ...this.cache,
      ...data,
      lastUpdate: Date.now(),
    };
  }

  /**
   * Clear cache (used on logout, errors, etc.)
   */
  private clearCache(): void {
    this.cache = {
      lastUpdate: 0,
      ttl: this.cache.ttl,
    };
  }

  /**
   * Transform transport errors to user-friendly auth errors
   */
  private transformError(transportError: any): AuthResult {
    const errorMap: Record<string, string> = {
      INVALID_CREDENTIALS: "Invalid email or password",
      USER_NOT_FOUND: "User not found",
      EMAIL_ALREADY_EXISTS: "Email is already registered",
      WEAK_PASSWORD: "Password does not meet security requirements",
      RATE_LIMITED: "Too many attempts. Please try again later",
      NETWORK_ERROR: "Connection error. Please check your internet connection",
      UNAUTHORIZED: "Session expired. Please sign in again",
      FORBIDDEN: "Access denied",
      VALIDATION_ERROR: "Invalid input data",
    };

    const code = transportError?.code || "UNKNOWN_ERROR";
    const message =
      errorMap[code] ||
      transportError?.message ||
      "An unexpected error occurred";

    return {
      success: false,
      error: {
        code,
        message,
        field: transportError?.field,
      },
    };
  }

  // IAuthService implementation

  async signIn(input: SignInInput): Promise<AuthResult> {
    try {
      const response = await this.transport.signIn(input);

      if (response.error) {
        this.clearCache();
        return this.transformError(response.error);
      }

      const authResult = response.data;
      if (!authResult) {
        return this.transformError({
          code: "NO_DATA",
          message: "No response data",
        });
      }

      if (authResult.success && authResult.user && authResult.sessionInfo) {
        // Update cache with successful login data
        this.updateCache({
          user: authResult.user,
          sessionInfo: authResult.sessionInfo,
          isAuthenticated: true,
        });

        if (this.config.debugMode) {
          console.log("User signed in successfully:", {
            userId: authResult.user.id,
            name: authResult.user.name,
            sessionDuration: authResult.sessionDuration,
          });
        }
      }

      return authResult;
    } catch (error) {
      this.clearCache();
      return this.transformError(error);
    }
  }

  async signUp(input: SignUpInput): Promise<AuthResult> {
    try {
      const response = await this.transport.signUp(input);

      if (response.error) {
        return this.transformError(response.error);
      }

      const authResult = response.data;
      if (!authResult) {
        return this.transformError({
          code: "NO_DATA",
          message: "No response data",
        });
      }

      if (authResult.success && authResult.user && authResult.sessionInfo) {
        // Update cache with new user data
        this.updateCache({
          user: authResult.user,
          sessionInfo: authResult.sessionInfo,
          isAuthenticated: true,
        });

        if (this.config.debugMode) {
          console.log("User signed up successfully:", {
            userId: authResult.user.id,
            name: authResult.user.name,
          });
        }
      }

      return authResult;
    } catch (error) {
      return this.transformError(error);
    }
  }

  async signOut(): Promise<boolean> {
    try {
      const response = await this.transport.signOut();

      // Always clear cache on signout, regardless of server response
      this.clearCache();

      if (response.error) {
        if (this.config.debugMode) {
          console.warn(
            "SignOut server error (cache cleared anyway):",
            response.error,
          );
        }
        // Return true because local logout succeeded even if server errored
        return true;
      }

      if (this.config.debugMode) {
        console.log("User signed out successfully");
      }

      return response.data || true;
    } catch (error) {
      // Always clear cache on signout attempt
      this.clearCache();
      if (this.config.debugMode) {
        console.warn("SignOut error (cache cleared anyway):", error);
      }
      // Return true because local logout succeeded
      return true;
    }
  }

  async refreshToken(): Promise<AuthResult> {
    try {
      const response = await this.transport.refreshToken();

      if (response.error) {
        this.clearCache();
        return this.transformError(response.error);
      }

      const authResult = response.data;
      if (!authResult) {
        this.clearCache();
        return this.transformError({
          code: "NO_DATA",
          message: "No response data",
        });
      }

      if (authResult.success && authResult.user && authResult.sessionInfo) {
        // Update cache with refreshed session data
        this.updateCache({
          user: authResult.user,
          sessionInfo: authResult.sessionInfo,
          isAuthenticated: true,
        });

        if (this.config.debugMode) {
          console.log("Token refreshed successfully");
        }
      }

      return authResult;
    } catch (error) {
      this.clearCache();
      return this.transformError(error);
    }
  }

  async getCurrentUser(): Promise<User | null> {
    // Return cached user if available and valid
    if (this.isCacheValid() && this.cache.user !== undefined) {
      return this.cache.user;
    }

    try {
      const response = await this.transport.getCurrentUser();

      if (response.error) {
        this.updateCache({ user: null, isAuthenticated: false });

        // Only log non-auth errors
        if (!this.isAuthError(response.error)) {
          console.warn("Get current user error:", response.error);
        }

        return null;
      }

      const user = response.data || null;
      this.updateCache({ user, isAuthenticated: !!user });

      return user;
    } catch (error) {
      this.updateCache({ user: null, isAuthenticated: false });

      // Only log non-auth errors
      if (!this.isAuthError(error)) {
        console.warn("Get current user failed:", error);
      }

      return null;
    }
  }

  async getSessionInfo(): Promise<SessionInfo | null> {
    // Return cached session info if available and valid
    if (this.isCacheValid() && this.cache.sessionInfo !== undefined) {
      return this.cache.sessionInfo;
    }

    try {
      const response = await this.transport.getSessionInfo();

      if (response.error) {
        this.updateCache({ sessionInfo: null, isAuthenticated: false });

        // Only log non-auth errors
        if (!this.isAuthError(response.error)) {
          console.warn("Session info error:", response.error);
        }

        return null;
      }

      const sessionInfo = response.data || null;
      this.updateCache({ sessionInfo, isAuthenticated: !!sessionInfo });

      return sessionInfo;
    } catch (error) {
      this.updateCache({ sessionInfo: null, isAuthenticated: false });

      // Only log non-auth errors
      if (!this.isAuthError(error)) {
        console.warn("Session info fetch failed:", error);
      }

      return null;
    }
  }

  private isAuthError(error: any): boolean {
    return (
      error?.code === "UNAUTHORIZED" ||
      error?.message?.includes("401") ||
      error?.message?.includes("Unauthorized") ||
      error?.status === 401
    );
  }

  async isAuthenticated(): Promise<boolean> {
    // Return cached auth status if available and valid (short TTL for auth checks)
    const authCacheTTL = 30 * 1000; // 30 seconds for auth status
    const isAuthCacheValid = Date.now() - this.cache.lastUpdate < authCacheTTL;

    if (isAuthCacheValid && this.cache.isAuthenticated !== undefined) {
      console.log(
        "🔍 isAuthenticated: Using cached result",
        this.cache.isAuthenticated,
      );
      return this.cache.isAuthenticated;
    }

    try {
      // ✅ NEW APPROACH: Use dedicated auth status endpoint without @authenticated directive
      // This prevents GraphQL authentication errors in console
      console.log(
        "🔍 isAuthenticated: Checking auth status via dedicated endpoint...",
      );
      const response = await this.transport.checkAuthStatus();

      if (response.error) {
        // Auth status check should never fail unless there's a network issue
        console.warn("🔍 isAuthenticated: Status check failed", response.error);
        this.updateCache({ isAuthenticated: false });
        return false;
      }

      const isAuth = response.data || false;
      console.log("🔍 isAuthenticated: Backend response", { isAuth });

      // Cache the result
      this.updateCache({ isAuthenticated: isAuth });

      return isAuth;
    } catch (error) {
      console.log("🔍 isAuthenticated: Backend check failed", error);
      this.updateCache({ isAuthenticated: false });

      if (this.config.debugMode) {
        console.warn("Authentication check failed:", error);
      }

      return false;
    }
  }

  // Tenant methods removed - tenants now handled by SuperTokens only

  async hasRole(role: string): Promise<boolean> {
    const sessionInfo = await this.getSessionInfo();
    return sessionInfo?.roles?.includes(role) || false;
  }

  async getUserRoles(): Promise<string[]> {
    const sessionInfo = await this.getSessionInfo();
    return sessionInfo?.roles || [];
  }

  /**
   * Utility methods for debugging and management
   */

  /**
   * Get current cache state (for debugging)
   */
  getCacheState() {
    return {
      ...this.cache,
      isCacheValid: this.isCacheValid(),
    };
  }

  /**
   * Force cache refresh
   */
  async forceCacheRefresh(): Promise<void> {
    this.clearCache();

    // Refresh key auth data
    await Promise.all([this.getCurrentUser(), this.getSessionInfo()]);
  }

  /**
   * Update cache TTL
   */
  setCacheTTL(ttlMs: number): void {
    this.cache.ttl = ttlMs;
  }

  /**
   * Clean up resources
   */
  async cleanup(): Promise<void> {
    this.clearCache();
    await this.transport.cleanup();
  }
}
