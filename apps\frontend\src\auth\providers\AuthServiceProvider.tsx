"use client";

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
} from "react";
import type {
  IAuthService,
  IAuthServiceFactory,
  AuthConfig,
  User,
} from "@/auth";
import { GraphQLAuthService } from "@/auth";
import { useAppDispatch, useAppSelector } from "@/hooks/store";
import {
  setAuthServiceInitialized,
  setInitError,
  checkAuthStatus as checkAuthStatusThunk,
  signOut as signOutThunk,
} from "@/features/auth/authSlice";
import { injectAuthService } from "@/store";

// Simplified Context Value - only service layer now
interface AuthServiceContextValue {
  authService: IAuthService | null;
  isInitialized: boolean;
  config: AuthConfig | null;
  error: string | null;
  // Auth state now comes from Redux, but we keep methods for backward compatibility
  checkAuthStatus: () => Promise<void>;
  signOut: () => Promise<void>;
}

const AuthServiceContext = createContext<AuthServiceContextValue>({
  authService: null,
  isInitialized: false,
  config: null,
  error: null,
  checkAuthStatus: async () => {},
  signOut: async () => {},
});

class DefaultAuthServiceFactory implements IAuthServiceFactory {
  constructor(private config: AuthConfig) {}

  createAuthService(): IAuthService {
    return new GraphQLAuthService(this.config);
  }
}

function createAuthConfig(): AuthConfig {
  const apiUrl = process.env.NEXT_PUBLIC_API_URL || "http://localhost:4000";
  const graphqlEndpoint = `${apiUrl}/graphql`;

  return {
    apiUrl,
    graphqlEndpoint,
    enableCaching: process.env.NODE_ENV !== "production",
    sessionRefreshInterval: 5 * 60 * 1000,
    rememberMeDefault: false,
    debugMode: process.env.NODE_ENV === "development",
  };
}

interface AuthServiceProviderProps {
  children: ReactNode;
  config?: Partial<AuthConfig>;
  factory?: IAuthServiceFactory;
}

export function AuthServiceProvider({
  children,
  config: customConfig,
  factory: customFactory,
}: AuthServiceProviderProps) {
  // Service layer state (remains in Context)
  const [authService, setAuthService] = useState<IAuthService | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [config, setConfig] = useState<AuthConfig | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Redux integration
  const dispatch = useAppDispatch();
  
  // Auth state now comes from Redux
  const authState = useAppSelector((state) => state.auth);

  // Service initialization (unchanged)
  useEffect(() => {
    async function initializeAuthService() {
      try {
        const baseConfig = createAuthConfig();
        const finalConfig: AuthConfig = {
          ...baseConfig,
          ...customConfig,
        };

        const factory =
          customFactory || new DefaultAuthServiceFactory(finalConfig);
        const service = factory.createAuthService();

        setAuthService(service);
        setConfig(finalConfig);
        setIsInitialized(true);
        setError(null);

        injectAuthService(service);

        // Notify Redux that service is initialized
        dispatch(setAuthServiceInitialized({ config: finalConfig }));

        if (finalConfig.debugMode) {
          console.log("Auth service initialized:", {
            config: finalConfig,
            serviceType: service.constructor.name,
          });
        }

        // Inject auth service into Redux store
        injectAuthService(service);
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Unknown initialization error";
        setError(errorMessage);
        setIsInitialized(false);
        
        // Notify Redux about initialization error
        dispatch(setInitError(errorMessage));
        
        console.error("Failed to initialize auth service:", error);
      }
    }

    initializeAuthService();
  }, [customConfig, customFactory, dispatch]);

  // Auto-check auth status when service is ready
  useEffect(() => {
    if (isInitialized && authService && !authState.isCheckingAuth) {
      // Dispatch Redux thunk instead of local state management
      dispatch(checkAuthStatusThunk());
    }
  }, [isInitialized, authService, dispatch, authState.isCheckingAuth]);

  // Auth methods that dispatch Redux actions
  const checkAuthStatus = async () => {
    if (!authService) return;
    // Dispatch Redux thunk
    await dispatch(checkAuthStatusThunk());
  };

  const signOut = async () => {
    if (!authService) return;
    // Dispatch Redux thunk
    await dispatch(signOutThunk());
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (authService && "cleanup" in authService) {
        const cleanup = (authService as any).cleanup;
        if (typeof cleanup === "function") {
          cleanup();
        }
      }
    };
  }, [authService]);

  const contextValue: AuthServiceContextValue = {
    authService,
    isInitialized,
    config,
    error,
    checkAuthStatus,
    signOut,
  };

  return (
    <AuthServiceContext.Provider value={contextValue}>
      {children}
    </AuthServiceContext.Provider>
  );
}

// Service layer hooks (unchanged)
export function useAuthService(): IAuthService {
  const context = useContext(AuthServiceContext);

  if (!context) {
    throw new Error(
      "useAuthService must be used within an AuthServiceProvider",
    );
  }

  if (context.error) {
    throw new Error(`Auth service initialization failed: ${context.error}`);
  }

  if (!context.isInitialized || !context.authService) {
    throw new Error("Auth service is not initialized yet");
  }

  return context.authService;
}

export function useAuthServiceState() {
  const context = useContext(AuthServiceContext);

  if (!context) {
    throw new Error(
      "useAuthServiceState must be used within an AuthServiceProvider",
    );
  }

  return {
    authService: context.authService,
    isInitialized: context.isInitialized,
    config: context.config,
    error: context.error,
    isLoading: !context.isInitialized && !context.error,
  };
}

// Updated hook - now uses Redux state but maintains backward compatibility
export function useAuth() {
  const context = useContext(AuthServiceContext);
  
  // Get auth state from Redux
  const authState = useAppSelector((state) => state.auth);

  if (!context) {
    throw new Error("useAuth must be used within an AuthServiceProvider");
  }

  return {
    // Auth state from Redux
    isAuthenticated: authState.isAuthenticated,
    user: authState.user,
    isCheckingAuth: authState.isCheckingAuth,
    authError: authState.authError,
    // Service state from Context
    isInitialized: context.isInitialized,
    // Methods from Context (now dispatch Redux actions)
    checkAuthStatus: context.checkAuthStatus,
    signOut: context.signOut,
  };
}

export function useAuthConfig(): AuthConfig | null {
  const context = useContext(AuthServiceContext);

  if (!context) {
    throw new Error("useAuthConfig must be used within an AuthServiceProvider");
  }

  return context.config;
}

export const AuthServiceDevTools = {
  getCurrentService(): IAuthService | null {
    const context = React.useContext(AuthServiceContext);
    return context?.authService || null;
  },

  async testConnectivity(): Promise<boolean> {
    const service = this.getCurrentService();
    if (!service) {
      return false;
    }

    try {
      return true;
    } catch {
      return false;
    }
  },

  getServiceStats() {
    const service = this.getCurrentService();
    if (!service) return null;

    return {
      serviceType: service.constructor.name,
      initialized: true,
    };
  },
};

export function withAuthService<P extends object>(
  Component: React.ComponentType<P & { authService: IAuthService }>,
) {
  return function AuthServiceHOC(props: P) {
    const authService = useAuthService();
    return <Component {...props} authService={authService} />;
  };
}
