import type { Request, Response } from "express";
import { PrismaClient } from "@pulsepanel/db/generated/client";
import type { Redis } from "ioredis";
import { randomUUID } from "crypto";
import Session from "supertokens-node/recipe/session/index.js";
import { SessionCacheService } from "./sessionCache.js";
import { UserMetadataService } from "./userMetadataService.js";
import type {
  Context,
  SecurityContext,
  UserContext,
  AuditContext,
} from "../types/context.js";
import type { AuthContext, SessionData } from "./types.js";

interface CreateContextOptions {
  req: Request & { authContext?: AuthContext };
  res: Response;
  prisma: PrismaClient;
  redis: Redis;
}

/**
 * Creates comprehensive GraphQL context with security features
 */
export async function createGraphQLContext({
  req,
  res,
  prisma,
  redis,
}: CreateContextOptions): Promise<Context> {
  const requestStartTime = Date.now();
  const requestId = randomUUID();

  // Extract security information
  const ipAddress =
    (req.headers["x-forwarded-for"] as string)?.split(",")[0] ||
    req.socket.remoteAddress ||
    "unknown";
  const userAgent = req.headers["user-agent"] || "unknown";

  // ✅ ИСПРАВЛЕНО: Auth проверка прямо в context factory
  const sessionCache = new SessionCacheService(redis);
  const userMetadata = new UserMetadataService(redis);
  let authContext: AuthContext = { isAuthenticated: false };

  try {
    console.log("🔐 Context Factory: Checking SuperTokens session");

    // SuperTokens сам читает HttpOnly куки автоматически
    const session = await Session.getSession(req, res, {
      sessionRequired: false,
      checkDatabase: true,
    });

    if (session) {
      const sessionHandle = session.getHandle();
      console.log("🔐 Context Factory: Found valid session", { sessionHandle });

      // Пытаемся получить из Redis кеша
      let sessionData = await sessionCache.getSession(sessionHandle);
      const fromCache = !!sessionData;

      if (!sessionData) {
        // Cache miss - создаем sessionData из SuperTokens
        const accessTokenPayload = session.getAccessTokenPayload();
        sessionData = {
          userId: session.getUserId(),
          tenantId: session.getTenantId(),
          userDataInJWT: accessTokenPayload,
          sessionHandle: session.getHandle(),
          roles: accessTokenPayload.roles || [],
        };

        // Кешируем валидную сессию
        await sessionCache.setSession(sessionHandle, sessionData, 300);
        console.log("🔐 Context Factory: Cached new session data");
      } else {
        console.log("🔐 Context Factory: Using cached session data");
      }

      authContext = {
        isAuthenticated: true,
        session: sessionData,
        userId: sessionData.userId,
        tenantId: sessionData.tenantId,
        fromCache,
      };
    } else {
      console.log("🔐 Context Factory: No valid session found");
    }
  } catch (error) {
    console.log("🔐 Context Factory: Session verification failed", error);
    authContext = { isAuthenticated: false, error };
  }

  // Build security context
  const security: SecurityContext = {
    ipAddress,
    userAgent,
    requestId,
    timestamp: new Date(),
  };

  // Build audit context
  const audit: AuditContext = {
    operationType: "query", // Will be updated by operation
    requestStartTime,
  };

  // Build user context if authenticated
  let user: UserContext | undefined;
  let currentTenant: Context["currentTenant"] | undefined;

  if (authContext.isAuthenticated && authContext.session) {
    console.log("🔐 Context Factory: Loading user from DB", {
      userId: authContext.session.userId,
      tenantId: authContext.session.tenantId,
    });

    // Fetch full user data from database using authProviderId
    const dbUser = await prisma.user.findUnique({
      where: { authProviderId: authContext.session.userId },
    });

    console.log("🔐 Context Factory: DB user result", {
      found: !!dbUser,
      userId: dbUser?.id,
    });

    if (dbUser) {
      // Get user display name from SuperTokens metadata with caching
      console.log("🔐 Context Factory: About to fetch display name", {
        supertokensId: authContext.session.userId,
        userIdType: typeof authContext.session.userId,
      });

      let displayName: string | null = null;
      try {
        displayName = await userMetadata.getUserDisplayName(
          authContext.session.userId,
        );
        console.log("🔐 Context Factory: Successfully got display name", {
          displayName,
        });
      } catch (error) {
        console.error("🔐 Context Factory: Error getting display name", error);
        displayName = null;
      }

      console.log(
        "🔐 Context Factory: Retrieved display name from SuperTokens",
        {
          supertokensId: authContext.session.userId,
          displayName,
        },
      );

      user = {
        id: dbUser.id,
        name: displayName || undefined, // ✅ Используем имя из SuperTokens, а не из Prisma
        tenantId: dbUser.tenantId,
        roles: authContext.session.roles || [],
        permissions: [], // Will be populated on demand
      };

      // TODO: Fetch tenant info from SuperTokens if needed
      currentTenant = {
        id: dbUser.tenantId,
        name: dbUser.tenantId === "public" ? "Public Tenant" : dbUser.tenantId,
        roles: authContext.session.roles || [],
      };
    }
  }

  // Create security event logger
  const logSecurityEvent = async (
    event: string,
    details?: Record<string, any>,
  ) => {
    const logData = {
      event,
      requestId,
      timestamp: new Date().toISOString(),
      ip: ipAddress,
      userAgent,
      userId: user?.id,
      tenantId: user?.tenantId,
      ...details,
    };

    // Log to Redis for real-time monitoring
    await redis.lpush("security_events", JSON.stringify(logData));
    await redis.expire("security_events", 86400); // 24 hour retention

    // Log to console for development
    if (process.env.NODE_ENV === "development") {
      console.log(`🔒 Security Event: ${event}`, logData);
    }
  };

  // Helper methods
  const requireAuth = () => {
    if (!authContext.isAuthenticated || !user) {
      throw new Error("Authentication required");
    }
  };

  const requireRole = (role: string) => {
    requireAuth();
    if (!user!.roles.includes(role)) {
      throw new Error(`Role '${role}' is required`);
    }
  };

  const requireTenantAccess = (tenantId: string) => {
    requireAuth();
    if (user!.tenantId !== tenantId) {
      throw new Error(`Access denied to tenant ${tenantId}`);
    }
  };

  return {
    // Express request/response
    req,
    res,

    // Database connections
    prisma,
    redis,

    // Authentication state
    isAuthenticated: authContext.isAuthenticated,
    session: authContext.session || undefined,

    // User context
    user,
    currentTenant,

    // Security context
    security,

    // Audit context
    audit,

    // Helper methods
    requireAuth,
    requireRole,
    requireTenantAccess,
    logSecurityEvent,
  };
}

/**
 * Middleware to add operation info to context
 */
export function addOperationInfoToContext(
  context: Context,
  operationName?: string | null,
) {
  context.audit.operationName = operationName || undefined;

  // Log operation start
  context.logSecurityEvent("graphql_operation_start", {
    operation: operationName,
    variables: context.audit.variables,
  });
}
