'use client';

import { ReactNode } from 'react';
import { StoreProvider } from './StoreProvider';
import { IAuthService } from '@/auth';

interface AppProvidersProps {
  children: ReactNode;
  authService?: IAuthService;
}

/**
 * Combined providers for the application
 * Order is important: StoreProvider -> Auth -> Apollo (when added)
 */
export function AppProviders({ children, authService }: AppProvidersProps) {
  return (
    <StoreProvider authService={authService}>
      {/* Future: AuthServiceProvider will go here when migrated */}
      {/* Future: ApolloProvider will go here */}
      {children}
    </StoreProvider>
  );
}

// Utility component for easier layout integration
export function withAppProviders(authService?: IAuthService) {
  return function AppProvidersWrapper({ children }: { children: ReactNode }) {
    return (
      <AppProviders authService={authService}>
        {children}
      </AppProviders>
    );
  };
} 