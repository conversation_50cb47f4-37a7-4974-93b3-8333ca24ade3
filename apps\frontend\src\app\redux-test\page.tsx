'use client';

import { useAuth, useAuthActions, useSidebar, useTheme, useNotifications, usePageInfo } from '@/hooks/features';

export default function ReduxTestPage() {
  const auth = useAuth();
  const authActions = useAuthActions();
  const sidebar = useSidebar();
  const theme = useTheme();
  const notifications = useNotifications();
  const pageInfo = usePageInfo();
  
  return (
    <div style={{ padding: '2rem' }}>
      <h1>Redux Store Test - Feature Hooks</h1>
      
      <div style={{ marginBottom: '2rem' }}>
        <h2>Current State:</h2>
        <p><strong>Sidebar:</strong> {sidebar.open ? 'Open' : 'Closed'} | Collapsed: {sidebar.collapsed ? 'Yes' : 'No'}</p>
        <p><strong>Theme:</strong> {theme.theme}</p>
        <p><strong>Page Title:</strong> {pageInfo.title}</p>
        <p><strong>Breadcrumbs:</strong> {pageInfo.breadcrumbs.length}</p>
        <p><strong>Notifications:</strong> {notifications.notifications.length}</p>
        <p><strong>Auth Status:</strong> {auth.isAuthenticated ? '✅ Authenticated' : '❌ Not Authenticated'}</p>
        <p><strong>Auth Checking:</strong> {auth.isCheckingAuth ? 'Yes' : 'No'}</p>
        <p><strong>Auth Initialized:</strong> {auth.isInitialized ? 'Yes' : 'No'}</p>
        <p><strong>Auth Error:</strong> {auth.authError || 'None'}</p>
      </div>
      
      <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
        <h3 style={{ width: '100%' }}>UI Actions:</h3>
        
        <button onClick={() => sidebar.toggle()}>
          Toggle Sidebar
        </button>
        
        <button onClick={() => sidebar.toggleCollapse()}>
          Toggle Sidebar Collapse
        </button>
        
        <button onClick={() => theme.setTheme('dark')}>
          Set Dark Theme
        </button>
        
        <button onClick={() => theme.setTheme('light')}>
          Set Light Theme
        </button>
        
        <button onClick={() => theme.setTheme('system')}>
          Set System Theme
        </button>
        
        <button onClick={() => notifications.add({
          type: 'success',
          message: `Test notification ${Date.now()}`,
          timeout: 3000
        })}>
          Add Success Notification
        </button>
        
        <button onClick={() => notifications.add({
          type: 'error',
          message: 'Error notification!',
          timeout: 5000
        })}>
          Add Error Notification
        </button>
        
        <button onClick={() => notifications.clear()}>
          Clear All Notifications
        </button>
        
        <button onClick={() => pageInfo.setTitle(`Test Title ${Date.now()}`)}>
          Update Page Title
        </button>
        
        <button onClick={() => pageInfo.setBreadcrumbs([
          { label: 'Home', href: '/' },
          { label: 'Test', href: '/redux-test' },
          { label: 'Current' }
        ])}>
          Set Breadcrumbs
        </button>
      </div>
      
      <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap', marginTop: '2rem' }}>
        <h3 style={{ width: '100%' }}>Auth Actions:</h3>
        
        <button onClick={() => authActions.setCheckingAuth(!auth.isCheckingAuth)}>
          Toggle Auth Check
        </button>
        
        <button onClick={() => authActions.clearAuthError()}>
          Clear Auth Error
        </button>
        
        <button onClick={() => authActions.checkAuthStatus()}>
          Check Auth Status (Async)
        </button>
      </div>
      
      {notifications.notifications.length > 0 && (
        <div style={{ marginTop: '2rem' }}>
          <h3>Active Notifications:</h3>
          {notifications.notifications.map(notification => (
            <div 
              key={notification.id}
              style={{ 
                padding: '0.5rem', 
                margin: '0.5rem 0',
                border: '1px solid #ccc',
                borderRadius: '4px',
                background: notification.type === 'error' ? '#fee' : '#efe'
              }}
            >
              <strong>{notification.type.toUpperCase()}:</strong> {notification.message}
              <button 
                onClick={() => notifications.remove(notification.id)}
                style={{ marginLeft: '1rem', padding: '0.2rem 0.5rem' }}
              >
                Remove
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
} 