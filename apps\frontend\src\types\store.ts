// Store types derived from actual store configuration
export type { RootState, AppStore } from '@/store';
import type { RootState } from '@/store';
import type { ThunkDispatch, UnknownAction } from '@reduxjs/toolkit';

// Properly typed dispatch for async thunks
export type AppDispatch = ThunkDispatch<RootState, any, UnknownAction>;

// Re-export slice types for convenience
export type { AuthState } from '@/features/auth/authSlice';
export type { UIState } from '@/features/ui/uiSlice';

// Redux hook types
export interface TypedUseSelectorHook<TState> {
  <TSelected = unknown>(
    selector: (state: TState) => TSelected,
    equalityFn?: (left: TSelected, right: TSelected) => boolean
  ): TSelected;
}

// Utility types for working with slices
export type SliceState<T> = T extends { getInitialState(): infer S } ? S : never;

// Action creator types
export interface ActionCreatorWithPayload<P, T extends string = string> {
  (payload: P): { type: T; payload: P };
  type: T;
}

export interface ActionCreatorWithoutPayload<T extends string = string> {
  (): { type: T };
  type: T;
}

// Async thunk types
export interface AsyncThunkAction<Returned, ThunkArg> {
  type: string;
  payload: Returned;
  meta: {
    arg: ThunkArg;
    requestId: string;
    requestStatus: 'fulfilled' | 'rejected';
  };
}

// Store configuration types
export interface StoreExtraArgument {
  authService?: import('@/auth').IAuthService;
}

// Middleware configuration
export type ThunkExtraArgument = StoreExtraArgument

// Common selector patterns
export type StateSelector<T> = (state: RootState) => T;
export type StateSliceSelector<K extends keyof RootState, T> = (slice: RootState[K]) => T;

// Feature flag type (for future use)
export interface FeatureFlags {
  enableAdvancedAuth: boolean;
  enableOfflineMode: boolean;
  enableAnalytics: boolean;
}

// Store enhancement types for development
export interface DevToolsConfig {
  name: string;
  trace: boolean;
  traceLimit: number;
  actionsDenylist?: string[];
  stateSanitizer?: (state: any) => any;
} 