import type { Request, Response } from "express";
import { PrismaClient } from "@pulsepanel/db/generated/client";
import Redis from "ioredis";
import EmailPassword from "supertokens-node/recipe/emailpassword/index.js";
import Session from "supertokens-node/recipe/session/index.js";
import { convertToRecipeUserId } from "supertokens-node";
import { randomUUID } from "crypto";
import { SessionCacheService } from "./sessionCache.js";
import { UserMetadataService } from "./userMetadataService.js";
import type { SessionData } from "./types.js";
import type { Context } from "../types/context.js";
import {
  getSessionDuration,
  getSuperTokensSessionConfig,
  buildLogoutCookieHeaders,
  getCookieDebugInfo,
} from "./cookieConfig.js";
import UserRoles from "supertokens-node/recipe/userroles/index.js";

export interface AuthError {
  code: string;
  message: string;
  field?: string;
}

export interface AuthResponse {
  success: boolean;
  user?: any;
  sessionInfo?: any;
  error?: AuthError;
  sessionDuration?: number; // TTL in seconds for frontend reference
}

export interface UserRegistrationData {
  email: string;
  password: string;
  name?: string;
  tenantId: string;
}

export interface SignInData {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export const DEFAULT_ROLE = "user";

/**
 * Enterprise-Grade Auth Service Layer
 * Integrates SuperTokens SDK with Prisma synchronization and Redis caching
 */
export class AuthService {
  private sessionCache: SessionCacheService;
  private userMetadata: UserMetadataService;

  constructor(
    private prisma: PrismaClient,
    private redis: Redis,
  ) {
    this.sessionCache = new SessionCacheService(redis);
    this.userMetadata = new UserMetadataService(redis);
  }

  /**
   * Cache warming: Preload role permissions at startup for better performance
   * Should be called when BFF starts up
   */
  async warmupPermissionsCache(): Promise<void> {
    try {
      console.log("🔥 Starting permissions cache warmup...");

      // Get all roles from SuperTokens
      const allRoles = await UserRoles.getAllRoles();

      if (allRoles.status !== "OK") {
        console.warn("⚠️ Failed to get roles for cache warmup");
        return;
      }

      let warmedRoles = 0;
      let errors = 0;

      // Warm up cache for each role
      for (const role of allRoles.roles) {
        try {
          await this.refreshRolePermissionsInCache(role);
          warmedRoles++;
        } catch (error) {
          console.error(`❌ Failed to warm cache for role '${role}':`, error);
          errors++;
        }
      }

      console.log(
        `✅ Cache warmup completed: ${warmedRoles} roles cached, ${errors} errors`,
      );
    } catch (error) {
      console.error("❌ Cache warmup failed:", error);
    }
  }

  /**
   * User Registration with Enterprise Security
   */
  async signUp(
    registrationData: UserRegistrationData,
    req: Request,
    res: Response,
    context: Context,
  ): Promise<AuthResponse> {
    try {
      // 1. Input validation and sanitization
      const validation = await this.validateRegistrationInput(registrationData);
      if (!validation.isValid) {
        return {
          success: false,
          error: {
            code: "VALIDATION_ERROR",
            message: validation.error!,
            field: validation.field,
          },
        };
      }

      // 2. Check tenant access permissions
      const tenantAccess = await this.validateTenantAccess(
        registrationData.tenantId,
        context,
      );
      if (!tenantAccess) {
        context.logSecurityEvent("unauthorized_tenant_access", {
          tenantId: registrationData.tenantId,
          email: registrationData.email,
        });
        return {
          success: false,
          error: {
            code: "TENANT_ACCESS_DENIED",
            message: "Access to this tenant is not permitted",
          },
        };
      }

      // 3. Create user in SuperTokens
      const signUpResponse = await EmailPassword.signUp(
        "public", // tenant ID for SuperTokens
        registrationData.email,
        registrationData.password,
      );

      if (signUpResponse.status !== "OK") {
        const error = this.handleSuperTokensError(signUpResponse);
        context.logSecurityEvent("signup_failed", {
          email: registrationData.email,
          error: error.code,
        });
        return {
          success: false,
          error,
        };
      }

      // 4. Generate our tracking ID and create/sync user in Prisma database
      const ourTrackingId = randomUUID();

      const user = await this.createPrismaUser({
        name: registrationData.name || null,
        authProviderId: signUpResponse.user.id,
        ourTrackingId: ourTrackingId,
        authProviderType: "supertokens",
        tenantId: registrationData.tenantId,
      });

      // 5. Create initial session with default role
      const sessionResponse = await Session.createNewSession(
        req,
        res,
        "public",
        convertToRecipeUserId(signUpResponse.user.id),
        {
          email: registrationData.email,
          tenantId: registrationData.tenantId,
          roles: [DEFAULT_ROLE],
        },
        {},
      );

      // 6. Assign default role in SuperTokens
      const roleAssignment = await this.assignRole(
        registrationData.tenantId,
        signUpResponse.user.id,
        DEFAULT_ROLE,
        context,
      );

      if (!roleAssignment.success) {
        context.logSecurityEvent("signup_role_assignment_failed", {
          userId: signUpResponse.user.id,
          email: registrationData.email,
          tenantId: registrationData.tenantId,
          role: DEFAULT_ROLE,
          error: roleAssignment.error,
        });
        // Continue with signup even if role assignment fails
      }

      // 7. Cache session data
      const sessionData: SessionData = {
        userId: signUpResponse.user.id,
        tenantId: registrationData.tenantId,
        userDataInJWT: sessionResponse.getAccessTokenPayload(),
        sessionHandle: sessionResponse.getHandle(),
        roles: [DEFAULT_ROLE],
      };

      await this.sessionCache.setSession(
        sessionResponse.getHandle(),
        sessionData,
        300, // 5 minutes TTL
      );

      // 8. Audit logging
      context.logSecurityEvent("user_signup_success", {
        userId: signUpResponse.user.id,
        email: registrationData.email,
        tenantId: registrationData.tenantId,
      });

      const sessionExpiry = await sessionResponse.getExpiry();

      return {
        success: true,
        user,
        sessionInfo: {
          userId: signUpResponse.user.id,
          sessionHandle: sessionResponse.getHandle(),
          expiresAt: new Date(sessionExpiry).toISOString(),
          issuedAt: new Date().toISOString(),
          tenantId: registrationData.tenantId,
          roles: [DEFAULT_ROLE],
        },
      };
    } catch (error) {
      context.logSecurityEvent("signup_error", {
        email: registrationData.email,
        error: error instanceof Error ? error.message : String(error),
      });

      return {
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Registration failed due to internal error",
        },
      };
    }
  }

  /**
   * User Sign In with Security Monitoring
   */
  async signIn(
    signInData: SignInData,
    req: Request,
    res: Response,
    context: Context,
  ): Promise<AuthResponse> {
    try {
      // 1. Input validation
      const validation = await this.validateSignInInput(signInData);
      if (!validation.isValid) {
        return {
          success: false,
          error: {
            code: "VALIDATION_ERROR",
            message: validation.error!,
            field: validation.field,
          },
        };
      }

      // 2. Check for rate limiting and failed attempts
      const rateLimitCheck = await this.checkRateLimit(
        signInData.email,
        context.security.ipAddress,
      );
      if (!rateLimitCheck.allowed) {
        context.logSecurityEvent("signin_rate_limited", {
          email: signInData.email,
          ipAddress: context.security.ipAddress,
          attemptsCount: rateLimitCheck.attempts,
        });

        return {
          success: false,
          error: {
            code: "RATE_LIMITED",
            message: `Too many failed attempts. Try again in ${rateLimitCheck.retryAfter} seconds`,
          },
        };
      }

      // 3. Authenticate with SuperTokens
      const signInResponse = await EmailPassword.signIn(
        "public",
        signInData.email,
        signInData.password,
      );

      if (signInResponse.status !== "OK") {
        // Record failed attempt
        await this.recordFailedSignIn(
          signInData.email,
          context.security.ipAddress,
        );

        const error = this.handleSuperTokensError(signInResponse);
        context.logSecurityEvent("signin_failed", {
          email: signInData.email,
          error: error.code,
          ipAddress: context.security.ipAddress,
        });

        return {
          success: false,
          error,
        };
      }

      // 4. Get user data from Prisma or create if not exists (SuperTokens → Prisma sync)
      let user = await this.getPrismaUserByAuthProviderId(
        signInResponse.user.id,
      );

      if (!user) {
        // User exists in SuperTokens but not in Prisma - auto-sync
        context.logSecurityEvent("user_auto_sync", {
          supertokensId: signInResponse.user.id,
          email: signInData.email,
          reason: "user_exists_in_supertokens_but_not_in_prisma",
        });

        try {
          // Use SuperTokens public tenant
          const tenantId = "public";

          // Generate our tracking ID for manual SuperTokens user
          const ourTrackingId = randomUUID();

          // Create user in Prisma to match SuperTokens
          user = await this.createPrismaUser({
            name: null,
            authProviderId: signInResponse.user.id,
            ourTrackingId: ourTrackingId,
            authProviderType: "supertokens",
            tenantId: tenantId,
          });

          context.logSecurityEvent("user_auto_sync_success", {
            userId: user.id,
            supertokensId: signInResponse.user.id,
            email: signInData.email,
            tenantId: tenantId,
          });
        } catch (syncError) {
          context.logSecurityEvent("user_auto_sync_failed", {
            supertokensId: signInResponse.user.id,
            email: signInData.email,
            error:
              syncError instanceof Error
                ? syncError.message
                : String(syncError),
          });

          return {
            success: false,
            error: {
              code: "USER_SYNC_ERROR",
              message: "Failed to synchronize user account",
            },
          };
        }
      }

      // 5. Get user roles from SuperTokens
      const rolesResponse = await this.getUserRoles(
        "public",
        signInResponse.user.id,
      );
      const roles =
        rolesResponse.success && rolesResponse.roles
          ? rolesResponse.roles
          : [DEFAULT_ROLE];

      // 6. Get session duration configuration based on rememberMe
      const sessionConfig = getSessionDuration(signInData.rememberMe);
      const superTokensConfig = getSuperTokensSessionConfig(
        signInData.rememberMe,
      );

      // 7. Create session with security metadata and dynamic TTL
      const sessionResponse = await Session.createNewSession(
        req,
        res,
        "public",
        convertToRecipeUserId(signInResponse.user.id),
        {
          email: signInData.email,
          tenantId: user.tenantId,
          roles,
          rememberMe: signInData.rememberMe || false,
        },
        {},
        {
          accessTokenValidity: superTokensConfig.accessTokenValidity,
          refreshTokenValidity: superTokensConfig.refreshTokenValidity,
        },
      );

      // 8. Cache session with security metadata and dynamic TTL
      const sessionData: SessionData = {
        userId: signInResponse.user.id,
        tenantId: user.tenantId,
        userDataInJWT: sessionResponse.getAccessTokenPayload(),
        sessionHandle: sessionResponse.getHandle(),
        roles,
      };

      await this.sessionCache.setSession(
        sessionResponse.getHandle(),
        sessionData,
        sessionConfig.sessionTTL, // Dynamic TTL based on rememberMe
      );

      // 9. SuperTokens automatically sets secure cookies with the configured policies
      // No manual cookie setting needed - SuperTokens handles this based on session config

      // 10. Security monitoring
      await this.detectSuspiciousSignIn(user, context);

      // 11. Clear failed attempts on successful login
      await this.clearFailedSignInAttempts(
        signInData.email,
        context.security.ipAddress,
      );

      // 12. Audit logging with rememberMe info
      context.logSecurityEvent("signin_success", {
        userId: signInResponse.user.id,
        email: signInData.email,
        tenantId: sessionData.tenantId,
        roles,
        rememberMe: signInData.rememberMe || false,
        sessionTTL: sessionConfig.sessionTTL,
      });

      const sessionExpiry = await sessionResponse.getExpiry();

      // 13. Debug logging in development
      if (process.env.NODE_ENV === "development") {
        console.log(
          "🍪 Cookie Debug Info:",
          getCookieDebugInfo(signInData.rememberMe),
        );
      }

      return {
        success: true,
        user,
        sessionInfo: {
          userId: signInResponse.user.id,
          sessionHandle: sessionResponse.getHandle(),
          expiresAt: new Date(sessionExpiry).toISOString(),
          issuedAt: new Date().toISOString(),
          tenantId: sessionData.tenantId,
          roles,
        },
        sessionDuration: sessionConfig.sessionTTL, // Frontend can use this for UI
      };
    } catch (error) {
      context.logSecurityEvent("signin_error", {
        email: signInData.email,
        error: error instanceof Error ? error.message : String(error),
      });

      return {
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Sign in failed due to internal error",
        },
      };
    }
  }

  /**
   * User Sign Out with Session Cleanup and Cookie Clearing
   */
  async signOut(
    sessionHandle: string,
    context: Context,
    res?: Response,
  ): Promise<boolean> {
    try {
      // 1. Get session data for audit
      const sessionData = await this.sessionCache.getSession(sessionHandle);

      // 2. Revoke session in SuperTokens (also clears cookies automatically)
      const revoked = await Session.revokeSession(sessionHandle);

      // 3. Clear from Redis cache
      await this.sessionCache.invalidateSession(sessionHandle);

      // 4. Additional cookie clearing headers (defensive approach)
      if (res) {
        const clearHeaders = buildLogoutCookieHeaders();
        Object.entries(clearHeaders).forEach(([key, value]) => {
          res.setHeader(key.replace("Set-Cookie-", "Set-Cookie"), value);
        });
      }

      // 5. Audit logging
      context.logSecurityEvent("signout_success", {
        sessionHandle,
        userId: sessionData?.userId,
        tenantId: sessionData?.tenantId,
        cookiesCleared: !!res,
      });

      return revoked;
    } catch (error) {
      context.logSecurityEvent("signout_error", {
        sessionHandle,
        error: error instanceof Error ? error.message : String(error),
      });
      return false;
    }
  }

  /**
   * Refresh Token with Security Checks
   */
  async refreshToken(
    req: Request,
    res: Response,
    context: Context,
  ): Promise<AuthResponse> {
    try {
      // 1. Get existing session data from request
      const session = await Session.getSession(req, res, {
        sessionRequired: true,
      });

      if (!session) {
        return {
          success: false,
          error: {
            code: "SESSION_EXPIRED",
            message: "Session has expired",
          },
        };
      }

      const sessionHandle = session.getHandle();

      // 2. Get existing session data from cache
      const sessionData = await this.sessionCache.getSession(sessionHandle);
      if (!sessionData) {
        return {
          success: false,
          error: {
            code: "SESSION_NOT_FOUND",
            message: "Session not found in cache",
          },
        };
      }

      // 3. Update cache with new session data
      const updatedSessionData: SessionData = {
        ...sessionData,
        userDataInJWT: session.getAccessTokenPayload(),
        sessionHandle: session.getHandle(),
      };

      await this.sessionCache.setSession(
        session.getHandle(),
        updatedSessionData,
        300,
      );

      // 4. Audit logging
      context.logSecurityEvent("token_refresh_success", {
        userId: sessionData.userId,
        sessionHandle: session.getHandle(),
      });

      const sessionExpiry = await session.getExpiry();

      return {
        success: true,
        sessionInfo: {
          userId: sessionData.userId,
          sessionHandle: session.getHandle(),
          expiresAt: new Date(sessionExpiry).toISOString(),
          issuedAt: new Date().toISOString(),
          tenantId: sessionData.tenantId,
          roles: sessionData.roles || [],
        },
      };
    } catch (error) {
      context.logSecurityEvent("token_refresh_error", {
        error: error instanceof Error ? error.message : String(error),
      });

      return {
        success: false,
        error: {
          code: "REFRESH_FAILED",
          message: "Token refresh failed",
        },
      };
    }
  }

  // Private helper methods

  private async validateRegistrationInput(data: UserRegistrationData): Promise<{
    isValid: boolean;
    error?: string;
    field?: string;
  }> {
    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
      return {
        isValid: false,
        error: "Please provide a valid email address",
        field: "email",
      };
    }

    // Password strength validation
    if (data.password.length < 8) {
      return {
        isValid: false,
        error: "Password must be at least 8 characters long",
        field: "password",
      };
    }

    // Email uniqueness is now handled by SuperTokens
    // No need to check in Prisma since email is not stored there

    return { isValid: true };
  }

  private async validateSignInInput(data: SignInData): Promise<{
    isValid: boolean;
    error?: string;
    field?: string;
  }> {
    if (!data.email || !data.password) {
      return {
        isValid: false,
        error: "Email and password are required",
      };
    }

    return { isValid: true };
  }

  private async validateTenantAccess(
    tenantId: string,
    context: Context,
  ): Promise<boolean> {
    // For now, only allow "public" tenant (SuperTokens default)
    return tenantId === "public";
  }

  private handleSuperTokensError(response: any): AuthError {
    switch (response.status) {
      case "EMAIL_ALREADY_EXISTS_ERROR":
        return {
          code: "EMAIL_EXISTS",
          message: "Email already registered",
          field: "email",
        };
      case "WRONG_CREDENTIALS_ERROR":
        return {
          code: "INVALID_CREDENTIALS",
          message: "Invalid email or password",
        };
      default:
        return {
          code: "AUTH_ERROR",
          message: "Authentication failed",
        };
    }
  }

  private async createPrismaUser(data: {
    name: string | null;
    authProviderId: string;
    ourTrackingId: string;
    authProviderType: string;
    tenantId: string;
  }) {
    return await this.prisma.user.create({
      data: {
        authProviderId: data.authProviderId,
        ourTrackingId: data.ourTrackingId,
        authProviderType: data.authProviderType,
        tenantId: data.tenantId,
        lastAuthSync: new Date(),
      },
    });
  }

  private async getPrismaUserByAuthProviderId(authProviderId: string) {
    return await this.prisma.user.findUnique({
      where: { authProviderId },
    });
  }

  // Method removed - tenants now handled by SuperTokens only

  private async checkRateLimit(
    email: string,
    ipAddress: string,
  ): Promise<{
    allowed: boolean;
    attempts?: number;
    retryAfter?: number;
  }> {
    const failedAttemptKey = `failed_attempts:${email}:${ipAddress}`;
    const attempts = await this.redis.get(failedAttemptKey);
    const attemptCount = attempts ? parseInt(attempts) : 0;

    if (attemptCount >= 5) {
      const ttl = await this.redis.ttl(failedAttemptKey);
      return {
        allowed: false,
        attempts: attemptCount,
        retryAfter: ttl > 0 ? ttl : 3600, // 1 hour default
      };
    }

    return { allowed: true };
  }

  private async recordFailedSignIn(
    email: string,
    ipAddress: string,
  ): Promise<void> {
    const failedAttemptKey = `failed_attempts:${email}:${ipAddress}`;
    await this.redis.incr(failedAttemptKey);
    await this.redis.expire(failedAttemptKey, 3600); // 1 hour expiry
  }

  private async clearFailedSignInAttempts(
    email: string,
    ipAddress: string,
  ): Promise<void> {
    const failedAttemptKey = `failed_attempts:${email}:${ipAddress}`;
    await this.redis.del(failedAttemptKey);
  }

  private async detectSuspiciousSignIn(
    user: any,
    context: Context,
  ): Promise<void> {
    // Store login metadata for security analysis
    const loginMetaKey = `login_meta:${user.id}`;
    const loginData = {
      timestamp: Date.now(),
      ipAddress: context.security.ipAddress,
      userAgent: context.security.userAgent,
    };

    await this.redis.lpush(loginMetaKey, JSON.stringify(loginData));
    await this.redis.ltrim(loginMetaKey, 0, 9); // Keep last 10 logins
    await this.redis.expire(loginMetaKey, 86400 * 7); // 7 days

    // Check for suspicious patterns (different implementation can be added)
    const recentLogins = await this.redis.lrange(loginMetaKey, 0, 4);
    if (recentLogins.length >= 3) {
      const ips = recentLogins.map((login) => JSON.parse(login).ipAddress);
      const uniqueIps = new Set(ips);

      if (uniqueIps.size >= 3) {
        context.logSecurityEvent("suspicious_login_pattern", {
          userId: user.id,
          name: user.name,
          uniqueIpCount: uniqueIps.size,
          recentIps: Array.from(uniqueIps),
        });
      }
    }
  }

  /**
   * Get user display name from SuperTokens metadata with caching
   */
  async getUserDisplayName(supertokensUserId: string): Promise<string | null> {
    return await this.userMetadata.getUserDisplayName(supertokensUserId);
  }

  /**
   * Get full user metadata from SuperTokens
   */
  async getUserFullMetadata(supertokensUserId: string) {
    return await this.userMetadata.getUserMetadata(supertokensUserId);
  }

  /**
   * Update user metadata in SuperTokens and invalidate cache
   */
  async updateUserMetadata(
    supertokensUserId: string,
    metadata: Record<string, any>,
  ): Promise<boolean> {
    return await this.userMetadata.updateUserMetadata(
      supertokensUserId,
      metadata,
    );
  }

  // ========================================================================
  // RBAC Methods - SuperTokens UserRoles Integration
  // ========================================================================

  /**
   * Assign role to user via SuperTokens UserRoles API
   * @param tenantId - SuperTokens tenant ID (default: "public")
   * @param userId - SuperTokens user ID
   * @param role - Role name (admin, manager, user)
   * @param context - Request context for auditing
   */
  async assignRole(
    tenantId: string = "public",
    userId: string,
    role: string,
    context: Context,
  ): Promise<{
    success: boolean;
    didUserAlreadyHaveRole?: boolean;
    error?: AuthError;
  }> {
    try {
      const response = await UserRoles.addRoleToUser(tenantId, userId, role);

      if (response.status === "UNKNOWN_ROLE_ERROR") {
        return {
          success: false,
          error: {
            code: "UNKNOWN_ROLE_ERROR",
            message: `Role '${role}' does not exist`,
          },
        };
      }

      // Update session cache with new roles
      await this.refreshUserRolesInCache(userId, tenantId);

      // Refresh role permissions cache proactively
      await this.refreshRolePermissionsInCache(role);

      // Audit logging
      context.logSecurityEvent("role_assigned", {
        userId,
        tenantId,
        role,
        didUserAlreadyHaveRole: response.didUserAlreadyHaveRole,
      });

      return {
        success: true,
        didUserAlreadyHaveRole: response.didUserAlreadyHaveRole,
      };
    } catch (error) {
      context.logSecurityEvent("role_assignment_error", {
        userId,
        tenantId,
        role,
        error: error instanceof Error ? error.message : String(error),
      });

      return {
        success: false,
        error: {
          code: "ROLE_ASSIGNMENT_ERROR",
          message: "Failed to assign role",
        },
      };
    }
  }

  /**
   * Remove role from user via SuperTokens UserRoles API
   * @param tenantId - SuperTokens tenant ID (default: "public")
   * @param userId - SuperTokens user ID
   * @param role - Role name (admin, manager, user)
   * @param context - Request context for auditing
   */
  async removeRole(
    tenantId: string = "public",
    userId: string,
    role: string,
    context: Context,
  ): Promise<{
    success: boolean;
    didUserHaveRole?: boolean;
    error?: AuthError;
  }> {
    try {
      const response = await UserRoles.removeUserRole(tenantId, userId, role);

      if (response.status === "UNKNOWN_ROLE_ERROR") {
        return {
          success: false,
          error: {
            code: "UNKNOWN_ROLE_ERROR",
            message: `Role '${role}' does not exist`,
          },
        };
      }

      // Update session cache with new roles
      await this.refreshUserRolesInCache(userId, tenantId);

      // Refresh role permissions cache proactively
      await this.refreshRolePermissionsInCache(role);

      // Audit logging
      context.logSecurityEvent("role_removed", {
        userId,
        tenantId,
        role,
        didUserHaveRole: response.didUserHaveRole,
      });

      return {
        success: true,
        didUserHaveRole: response.didUserHaveRole,
      };
    } catch (error) {
      context.logSecurityEvent("role_removal_error", {
        userId,
        tenantId,
        role,
        error: error instanceof Error ? error.message : String(error),
      });

      return {
        success: false,
        error: {
          code: "ROLE_REMOVAL_ERROR",
          message: "Failed to remove role",
        },
      };
    }
  }

  /**
   * Get user roles from SuperTokens UserRoles API
   * @param tenantId - SuperTokens tenant ID (default: "public")
   * @param userId - SuperTokens user ID
   */
  async getUserRoles(
    tenantId: string = "public",
    userId: string,
  ): Promise<{
    success: boolean;
    roles?: string[];
    error?: AuthError;
  }> {
    try {
      const response = await UserRoles.getRolesForUser(tenantId, userId);

      return {
        success: true,
        roles: response.roles,
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: "ROLES_FETCH_ERROR",
          message: "Failed to fetch user roles",
        },
      };
    }
  }

  /**
   * Get all users with specific role from SuperTokens UserRoles API
   * @param tenantId - SuperTokens tenant ID (default: "public")
   * @param role - Role name to search for
   */
  async getUsersWithRole(
    tenantId: string = "public",
    role: string,
  ): Promise<{
    success: boolean;
    users?: string[];
    error?: AuthError;
  }> {
    try {
      const response = await UserRoles.getUsersThatHaveRole(tenantId, role);

      if (response.status === "UNKNOWN_ROLE_ERROR") {
        return {
          success: false,
          error: {
            code: "UNKNOWN_ROLE_ERROR",
            message: `Role '${role}' does not exist`,
          },
        };
      }

      return {
        success: true,
        users: response.users,
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: "USERS_WITH_ROLE_FETCH_ERROR",
          message: "Failed to fetch users with role",
        },
      };
    }
  }

  /**
   * Create role with permissions via SuperTokens UserRoles API
   * @param role - Role name to create
   * @param permissions - Array of permissions for the role
   */
  async createRoleWithPermissions(
    role: string,
    permissions: string[] = [],
  ): Promise<{
    success: boolean;
    createdNewRole?: boolean;
    error?: AuthError;
  }> {
    try {
      const response = await UserRoles.createNewRoleOrAddPermissions(
        role,
        permissions,
      );

      // Refresh role permissions cache with the new permissions
      await this.refreshRolePermissionsInCache(role, permissions);

      return {
        success: true,
        createdNewRole: response.createdNewRole,
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: "ROLE_CREATION_ERROR",
          message: "Failed to create role",
        },
      };
    }
  }

  /**
   * Refresh user roles in session cache after role changes
   * @param userId - SuperTokens user ID
   * @param tenantId - SuperTokens tenant ID
   */
  private async refreshUserRolesInCache(
    userId: string,
    tenantId: string = "public",
  ): Promise<void> {
    try {
      // Get updated roles from SuperTokens
      const rolesResponse = await this.getUserRoles(tenantId, userId);

      if (!rolesResponse.success || !rolesResponse.roles) {
        return;
      }

      // Update all active session caches for this user
      const sessionKeys = await this.redis.keys(`session:*`);

      for (const sessionKey of sessionKeys) {
        const sessionData = await this.sessionCache.getSession(
          sessionKey.replace("session:", ""),
        );

        if (sessionData && sessionData.userId === userId) {
          // Update roles in session data
          sessionData.roles = rolesResponse.roles;

          // Save updated session back to cache
          await this.sessionCache.setSession(
            sessionKey.replace("session:", ""),
            sessionData,
            300, // 5 minutes TTL
          );
        }
      }

      // Invalidate user permissions cache to force recomputation
      await this.redis.del(`permissions:${userId}:${tenantId}`);
    } catch (error) {
      console.error("Failed to refresh user roles in cache:", error);
    }
  }

  /**
   * Refresh role permissions in cache after role/permission changes
   * Proactive cache update instead of deletion for better performance
   * @param role - Role name that was modified
   * @param permissions - New permissions for the role (optional, fetched if not provided)
   */
  async refreshRolePermissionsInCache(
    role: string,
    permissions?: string[],
  ): Promise<void> {
    try {
      let rolePermissions = permissions;

      // If permissions not provided, fetch from SuperTokens
      if (!rolePermissions) {
        const response = await UserRoles.getPermissionsForRole(role);
        if (response.status === "OK") {
          rolePermissions = response.permissions;
        } else {
          console.warn(
            `⚠️ Role '${role}' not found in SuperTokens during cache refresh`,
          );
          return;
        }
      }

      // Proactive update: set new permissions in cache
      const cacheKey = `permissions:role:${role}`;
      if (rolePermissions && rolePermissions.length > 0) {
        await this.redis.set(cacheKey, JSON.stringify(rolePermissions));
        console.log(
          `✅ Refreshed permissions cache for role '${role}' with ${rolePermissions.length} permissions`,
        );
      } else {
        // If role has no permissions, remove from cache
        await this.redis.del(cacheKey);
        console.log(`🗑️ Removed empty permissions cache for role '${role}'`);
      }

      // Invalidate all user permission caches that might be affected by this role change
      await this.invalidateUserPermissionsByRole(role);
    } catch (error) {
      console.error(
        `❌ Failed to refresh permissions cache for role '${role}':`,
        error,
      );
      // Fallback: delete the cache entry to force fresh fetch
      await this.redis.del(`permissions:role:${role}`);
    }
  }

  /**
   * Invalidate user permission caches for all users with a specific role
   * @param role - Role that was modified
   */
  private async invalidateUserPermissionsByRole(role: string): Promise<void> {
    try {
      // Get all users with this role
      const usersResponse = await this.getUsersWithRole("public", role);

      if (usersResponse.success && usersResponse.users) {
        const pipeline = this.redis.pipeline();

        for (const userId of usersResponse.users) {
          // We don't know the exact tenantId, so we'll use pattern matching
          const userPermissionKeys = await this.redis.keys(
            `permissions:${userId}:*`,
          );
          userPermissionKeys.forEach((key) => {
            pipeline.del(key);
          });
        }

        await pipeline.exec();
        console.log(
          `🔄 Invalidated permission caches for ${usersResponse.users.length} users with role '${role}'`,
        );
      }
    } catch (error) {
      console.error(
        `❌ Failed to invalidate user permissions for role '${role}':`,
        error,
      );
    }
  }

  /**
   * *******: Batch updates для массовых изменений ролей
   * Efficiently update roles for multiple users with optimized cache management
   * @param operations - Array of role operations to perform
   */
  async batchUpdateUserRoles(
    operations: Array<{
      userId: string;
      tenantId?: string;
      action: "assign" | "remove";
      role: string;
    }>,
    context: Context,
  ): Promise<{
    success: boolean;
    results: Array<{
      userId: string;
      role: string;
      action: string;
      success: boolean;
      error?: string;
    }>;
    stats: {
      total: number;
      successful: number;
      failed: number;
    };
  }> {
    const results: Array<{
      userId: string;
      role: string;
      action: string;
      success: boolean;
      error?: string;
    }> = [];

    let successful = 0;
    let failed = 0;

    try {
      // Group operations by role for efficient cache management
      const roleGroups = new Map<string, typeof operations>();

      for (const op of operations) {
        if (!roleGroups.has(op.role)) {
          roleGroups.set(op.role, []);
        }
        roleGroups.get(op.role)!.push(op);
      }

      // Process each role group
      for (const [role, roleOps] of roleGroups) {
        console.log(
          `🔄 Processing batch operations for role '${role}': ${roleOps.length} operations`,
        );

        // Execute all operations for this role
        for (const op of roleOps) {
          try {
            const tenantId = op.tenantId || "public";
            let result;

            if (op.action === "assign") {
              result = await this.assignRole(
                tenantId,
                op.userId,
                op.role,
                context,
              );
            } else {
              result = await this.removeRole(
                tenantId,
                op.userId,
                op.role,
                context,
              );
            }

            results.push({
              userId: op.userId,
              role: op.role,
              action: op.action,
              success: result.success,
              error: result.error?.message,
            });

            if (result.success) {
              successful++;
            } else {
              failed++;
            }
          } catch (error) {
            results.push({
              userId: op.userId,
              role: op.role,
              action: op.action,
              success: false,
              error: error instanceof Error ? error.message : "Unknown error",
            });
            failed++;
          }
        }

        // Batch refresh role permissions cache once per role
        await this.refreshRolePermissionsInCache(role);
      }

      console.log(
        `✅ Batch role update completed: ${successful} successful, ${failed} failed`,
      );

      return {
        success: failed === 0,
        results,
        stats: {
          total: operations.length,
          successful,
          failed,
        },
      };
    } catch (error) {
      console.error("❌ Batch role update failed:", error);
      return {
        success: false,
        results,
        stats: {
          total: operations.length,
          successful,
          failed,
        },
      };
    }
  }

  /**
   * *******: Cascade updates при изменении разрешений роли
   * Update role permissions and cascade changes to all affected users
   * @param role - Role to update
   * @param newPermissions - New permissions for the role
   */
  async cascadeRolePermissionUpdate(
    role: string,
    newPermissions: string[],
  ): Promise<{
    success: boolean;
    affectedUsers: number;
    cacheUpdates: number;
    error?: string;
  }> {
    try {
      console.log(
        `🔄 Starting cascade update for role '${role}' with ${newPermissions.length} permissions`,
      );

      // 1. Update role permissions in SuperTokens
      const updateResponse = await UserRoles.createNewRoleOrAddPermissions(
        role,
        newPermissions,
      );

      if (!updateResponse) {
        throw new Error("Failed to update role permissions in SuperTokens");
      }

      // 2. Get all users with this role before cache updates
      const usersResponse = await this.getUsersWithRole("public", role);
      const affectedUsers = usersResponse.success
        ? usersResponse.users?.length || 0
        : 0;

      // 3. Proactively update role permissions cache
      await this.refreshRolePermissionsInCache(role, newPermissions);

      // 4. Cascade invalidate all user permission caches for affected users
      let cacheUpdates = 0;
      if (usersResponse.success && usersResponse.users) {
        const pipeline = this.redis.pipeline();

        for (const userId of usersResponse.users) {
          // Invalidate user permission caches (all tenants)
          const userPermissionKeys = await this.redis.keys(
            `permissions:${userId}:*`,
          );
          userPermissionKeys.forEach((key) => {
            pipeline.del(key);
            cacheUpdates++;
          });

          // Update session caches with new role permissions
          const sessionKeys = await this.redis.keys(`session:*`);
          for (const sessionKey of sessionKeys) {
            const sessionData = await this.sessionCache.getSession(
              sessionKey.replace("session:", ""),
            );

            if (sessionData && sessionData.userId === userId) {
              // Force recomputation of permissions on next request
              sessionData.permissionsLastUpdated = Date.now();
              await this.sessionCache.setSession(
                sessionKey.replace("session:", ""),
                sessionData,
                300, // 5 minutes TTL
              );
              cacheUpdates++;
            }
          }
        }

        await pipeline.exec();
      }

      console.log(
        `✅ Cascade update completed for role '${role}': ${affectedUsers} users, ${cacheUpdates} cache updates`,
      );

      return {
        success: true,
        affectedUsers,
        cacheUpdates,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      console.error(
        `❌ Cascade update failed for role '${role}':`,
        errorMessage,
      );

      return {
        success: false,
        affectedUsers: 0,
        cacheUpdates: 0,
        error: errorMessage,
      };
    }
  }

  /**
   * *******: Cleanup при удалении ролей
   * Safely delete a role and clean up all associated caches and user assignments
   * @param role - Role to delete
   */
  async cleanupDeletedRole(role: string): Promise<{
    success: boolean;
    removedFromUsers: number;
    cachesCleaned: number;
    error?: string;
  }> {
    try {
      console.log(`🗑️ Starting cleanup for deleted role '${role}'`);

      // 1. Get all users with this role before deletion
      const usersResponse = await this.getUsersWithRole("public", role);
      const usersWithRole = usersResponse.success
        ? usersResponse.users || []
        : [];

      // 2. Remove role from all users
      let removedFromUsers = 0;
      if (usersWithRole.length > 0) {
        const pipeline = this.redis.pipeline();

        for (const userId of usersWithRole) {
          try {
            // Remove role from user in SuperTokens
            const removeResponse = await UserRoles.removeUserRole(
              "public",
              userId,
              role,
            );
            if (removeResponse.status === "OK") {
              removedFromUsers++;
            }

            // Invalidate user permission caches
            const userPermissionKeys = await this.redis.keys(
              `permissions:${userId}:*`,
            );
            userPermissionKeys.forEach((key) => {
              pipeline.del(key);
            });

            // Update user session caches
            await this.refreshUserRolesInCache(userId, "public");
          } catch (error) {
            console.warn(
              `⚠️ Failed to remove role '${role}' from user '${userId}':`,
              error,
            );
          }
        }

        await pipeline.exec();
      }

      // 3. Delete role from SuperTokens
      try {
        await UserRoles.deleteRole(role);
        console.log(`✅ Deleted role '${role}' from SuperTokens`);
      } catch (error) {
        console.warn(
          `⚠️ Failed to delete role '${role}' from SuperTokens:`,
          error,
        );
      }

      // 4. Clean up role permissions cache
      let cachesCleaned = 0;
      const rolePermissionKey = `permissions:role:${role}`;
      const deleted = await this.redis.del(rolePermissionKey);
      cachesCleaned += deleted;

      // 5. Clean up any orphaned caches related to this role
      const orphanedKeys = await this.redis.keys(`*:${role}:*`);
      if (orphanedKeys.length > 0) {
        const deletedOrphaned = await this.redis.del(...orphanedKeys);
        cachesCleaned += deletedOrphaned;
      }

      console.log(
        `✅ Role cleanup completed for '${role}': removed from ${removedFromUsers} users, cleaned ${cachesCleaned} caches`,
      );

      return {
        success: true,
        removedFromUsers,
        cachesCleaned,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      console.error(`❌ Role cleanup failed for '${role}':`, errorMessage);

      return {
        success: false,
        removedFromUsers: 0,
        cachesCleaned: 0,
        error: errorMessage,
      };
    }
  }
}
