import supertokens from "supertokens-node";
import Session from "supertokens-node/recipe/session/index.js";
import EmailPassword from "supertokens-node/recipe/emailpassword/index.js";
import Dashboard from "supertokens-node/recipe/dashboard/index.js";
import MultiTenancy from "supertokens-node/recipe/multitenancy/index.js";
import UserRoles from "supertokens-node/recipe/userroles/index.js";
import UserMetadata from "supertokens-node/recipe/usermetadata/index.js";

const SUPERTOKENS_CORE_URL =
  process.env.SUPERTOKENS_CORE_URL || "http://localhost:3567";
const API_DOMAIN = process.env.API_DOMAIN || "http://localhost:3001";
const WEBSITE_DOMAIN = process.env.WEBSITE_DOMAIN || "http://localhost:3000";
const API_BASE_PATH = process.env.API_BASE_PATH || "/auth";
const WEBSITE_BASE_PATH = process.env.WEBSITE_BASE_PATH || "/auth";

// PostgreSQL connection parameters for SuperTokens
const POSTGRES_HOST = process.env.POSTGRES_HOST || "postgres";
const POSTGRES_PORT = process.env.POSTGRES_PORT || "5432";
const POSTGRES_USER = process.env.POSTGRES_USER || "pulsepanel_user";
const POSTGRES_PASSWORD =
  process.env.POSTGRES_PASSWORD || "pulsepanel_password";
const POSTGRES_DB = process.env.POSTGRES_DB || "pulsepanel_dev_db";

// PostgreSQL connection URI for SuperTokens with auth schema
const POSTGRES_CONNECTION_URI = `postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}?options=-c%20search_path=auth`;

export const printSuperTokensConfig = () => {
  console.log("🔧 Initializing SuperTokens...");
  console.log(`📡 SuperTokens Core URL: ${SUPERTOKENS_CORE_URL}`);
  console.log(
    `🔑 API Key: ${process.env.SUPERTOKENS_API_KEY || "dev-api-key"}`,
  );
  console.log(
    `🗄️  PostgreSQL URI: ${POSTGRES_CONNECTION_URI.replace(POSTGRES_PASSWORD, "***")}`,
  );
};

supertokens.init({
  framework: "express",
  supertokens: {
    connectionURI: SUPERTOKENS_CORE_URL,
    apiKey: process.env.SUPERTOKENS_API_KEY || "dev-api-key-secure-2024",
    networkInterceptor: (request, userContext) => {
      if (process.env.NODE_ENV === "development") {
        console.log(`🌐 SuperTokens request: ${request.method} ${request.url}`);
      }
      return request;
    },
  },
  appInfo: {
    appName: "PulsePanel Auth",
    apiDomain: API_DOMAIN,
    websiteDomain: WEBSITE_DOMAIN,
    apiBasePath: API_BASE_PATH,
    websiteBasePath: WEBSITE_BASE_PATH,
    apiGatewayPath: process.env.API_GATEWAY_PATH || "",
  },
  recipeList: [
    EmailPassword.init({
      signUpFeature: {
        formFields: [
          {
            id: "email",
            validate: async (value) => {
              if (typeof value !== "string") {
                return "Please provide a valid email";
              }
              const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
              if (!emailRegex.test(value)) {
                return "Please provide a valid email address";
              }
              return undefined;
            },
          },
          {
            id: "password",
            validate: async (value) => {
              if (typeof value !== "string") {
                return "Please provide a valid password";
              }
              if (value.length < 8) {
                return "Password must be at least 8 characters long";
              }
              return undefined;
            },
          },
        ],
      },
      override: {
        apis: (originalImplementation) => {
          return {
            ...originalImplementation,
            signUpPOST: async function (input) {
              return originalImplementation.signUpPOST!(input);
            },
          };
        },
      },
    }),
    Session.init({
      cookieDomain:
        process.env.NODE_ENV === "production"
          ? process.env.COOKIE_DOMAIN
          : undefined,
      cookieSecure: process.env.NODE_ENV === "production",
      sessionExpiredStatusCode: 401,
      antiCsrf: process.env.NODE_ENV === "production" ? "VIA_TOKEN" : "NONE",
      cookieSameSite: process.env.NODE_ENV === "production" ? "none" : "lax",
    }),
    MultiTenancy.init({
      getAllowedDomainsForTenantId: async (tenantId) => {
        console.log(`🏢 Checking domains for tenant: ${tenantId}`);
        return [WEBSITE_DOMAIN, API_DOMAIN];
      },
      override: {
        apis: (originalImplementation) => {
          return {
            ...originalImplementation,
          };
        },
      },
    }),
    UserRoles.init({}),
    UserMetadata.init(),
    Dashboard.init({
      apiKey: process.env.SUPERTOKENS_API_KEY || "dev-api-key",
      override: {
        functions: (originalImplementation) => {
          return {
            ...originalImplementation,
          };
        },
      },
    }),
  ],
  telemetry: process.env.NODE_ENV !== "production",
});

console.log("✅ SuperTokens initialized successfully");
console.log(
  `📊 Dashboard available at: ${API_DOMAIN}${API_BASE_PATH}/dashboard`,
);
