import { createSlice, createAsyncThunk, createSelector, type PayloadAction } from '@reduxjs/toolkit';
import type { User, AuthConfig, IAuthService, AuthResult, SignInInput, SignUpInput } from '@/auth';

// Auth State Interface based on current AuthServiceProvider
export interface AuthState {
  // Authentication status
  isAuthenticated: boolean;
  isCheckingAuth: boolean;
  
  // User data
  user: User | null;
  
  // Service initialization
  isInitialized: boolean;
  config: AuthConfig | null;
  
  // Error states
  authError: string | null;
  initError: string | null;
  
  // Loading states for specific operations
  isSigningIn: boolean;
  isSigningUp: boolean;
  isSigningOut: boolean;
  isRefreshing: boolean;
  
  // Session metadata
  sessionInfo: {
    expiresAt?: string;
    lastCheckedAt?: string;
    roles: string[];
  };
}

// Initial state
const initialState: AuthState = {
  isAuthenticated: false,
  isCheckingAuth: true,
  user: null,
  isInitialized: false,
  config: null,
  authError: null,
  initError: null,
  isSigningIn: false,
  isSigningUp: false,
  isSigningOut: false,
  isRefreshing: false,
  sessionInfo: {
    roles: [],
  },
};

// Async Thunks for auth operations
// Note: AuthService will be injected via middleware with getter pattern
interface ThunkAPI {
  state: { auth: AuthState };
  extra: { authService: IAuthService | undefined };
}

export const checkAuthStatus = createAsyncThunk<
  { isAuthenticated: boolean; user: User | null },
  void,
  ThunkAPI
>('auth/checkAuthStatus', async (_, { extra: { authService }, rejectWithValue }) => {
  try {
    if (!authService) {
      return rejectWithValue('Auth service not initialized');
    }
    
    const isAuthenticated = await authService.isAuthenticated();
    
    if (isAuthenticated) {
      const user = await authService.getCurrentUser();
      return { isAuthenticated, user };
    }
    
    return { isAuthenticated: false, user: null };
  } catch (error) {
    const message = error instanceof Error ? error.message : 'Failed to check auth status';
    return rejectWithValue(message);
  }
});

export const signIn = createAsyncThunk<
  AuthResult,
  SignInInput,
  ThunkAPI
>('auth/signIn', async (credentials, { extra: { authService }, rejectWithValue }) => {
  try {
    if (!authService) {
      return rejectWithValue('Auth service not initialized');
    }
    const result = await authService.signIn(credentials);
    return result;
  } catch (error) {
    const message = error instanceof Error ? error.message : 'Sign in failed';
    return rejectWithValue(message);
  }
});

export const signUp = createAsyncThunk<
  AuthResult,
  SignUpInput,
  ThunkAPI
>('auth/signUp', async (userData, { extra: { authService }, rejectWithValue }) => {
  try {
    if (!authService) {
      return rejectWithValue('Auth service not initialized');
    }
    const result = await authService.signUp(userData);
    return result;
  } catch (error) {
    const message = error instanceof Error ? error.message : 'Sign up failed';
    return rejectWithValue(message);
  }
});

export const signOut = createAsyncThunk<
  boolean,
  void,
  ThunkAPI
>('auth/signOut', async (_, { extra: { authService }, rejectWithValue }) => {
  try {
    if (!authService) {
      // Even without service, clear local state
      console.warn('Auth service not available, clearing local state');
      return true;
    }
    const success = await authService.signOut();
    return success;
  } catch (error) {
    // Even if server sign out fails, clear local state
    console.warn('Sign out error (clearing local state):', error);
    return true;
  }
});

export const refreshToken = createAsyncThunk<
  AuthResult,
  void,
  ThunkAPI
>('auth/refreshToken', async (_, { extra: { authService }, rejectWithValue }) => {
  try {
    if (!authService) {
      return rejectWithValue('Auth service not initialized');
    }
    const result = await authService.refreshToken();
    return result;
  } catch (error) {
    const message = error instanceof Error ? error.message : 'Token refresh failed';
    return rejectWithValue(message);
  }
});

// Auth Slice with modern RTK 2.0 syntax
export const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // Initialization actions
    setAuthServiceInitialized: (state, action: PayloadAction<{ config: AuthConfig }>) => {
      state.isInitialized = true;
      state.config = action.payload.config;
      state.initError = null;
    },
    
    setInitError: (state, action: PayloadAction<string>) => {
      state.initError = action.payload;
      state.isInitialized = false;
    },
    
    // Clear error states
    clearAuthError: (state) => {
      state.authError = null;
    },
    
    clearInitError: (state) => {
      state.initError = null;
    },
    
    // Update session metadata
    updateSessionInfo: (state, action: PayloadAction<Partial<AuthState['sessionInfo']>>) => {
      state.sessionInfo = { ...state.sessionInfo, ...action.payload };
    },
    
    // Local state updates (for immediate UI feedback)
    setCheckingAuth: (state, action: PayloadAction<boolean>) => {
      state.isCheckingAuth = action.payload;
    },
  },
  
  // Handle async thunk actions
  extraReducers: (builder) => {
    // Check Auth Status
    builder
      .addCase(checkAuthStatus.pending, (state) => {
        state.isCheckingAuth = true;
        state.authError = null;
      })
      .addCase(checkAuthStatus.fulfilled, (state, action) => {
        state.isCheckingAuth = false;
        state.isAuthenticated = action.payload.isAuthenticated;
        state.user = action.payload.user;
        state.sessionInfo.lastCheckedAt = new Date().toISOString();
      })
      .addCase(checkAuthStatus.rejected, (state, action) => {
        state.isCheckingAuth = false;
        state.isAuthenticated = false;
        state.user = null;
        state.authError = action.payload as string;
      });

    // Sign In
    builder
      .addCase(signIn.pending, (state) => {
        state.isSigningIn = true;
        state.authError = null;
      })
      .addCase(signIn.fulfilled, (state, action) => {
        state.isSigningIn = false;
        const result = action.payload;
        
        if (result.success && result.user) {
          state.isAuthenticated = true;
          state.user = result.user;
          state.sessionInfo.expiresAt = result.sessionInfo?.expiresAt;
          state.sessionInfo.roles = result.sessionInfo?.roles || [];
        } else {
          state.authError = result.error?.message || 'Sign in failed';
        }
      })
      .addCase(signIn.rejected, (state, action) => {
        state.isSigningIn = false;
        state.authError = action.payload as string;
      });

    // Sign Up
    builder
      .addCase(signUp.pending, (state) => {
        state.isSigningUp = true;
        state.authError = null;
      })
      .addCase(signUp.fulfilled, (state, action) => {
        state.isSigningUp = false;
        const result = action.payload;
        
        if (result.success && result.user) {
          state.isAuthenticated = true;
          state.user = result.user;
          state.sessionInfo.expiresAt = result.sessionInfo?.expiresAt;
          state.sessionInfo.roles = result.sessionInfo?.roles || [];
        } else {
          state.authError = result.error?.message || 'Sign up failed';
        }
      })
      .addCase(signUp.rejected, (state, action) => {
        state.isSigningUp = false;
        state.authError = action.payload as string;
      });

    // Sign Out
    builder
      .addCase(signOut.pending, (state) => {
        state.isSigningOut = true;
      })
      .addCase(signOut.fulfilled, (state) => {
        state.isSigningOut = false;
        state.isAuthenticated = false;
        state.user = null;
        state.sessionInfo.expiresAt = undefined;
        state.sessionInfo.roles = [];
        state.authError = null;
      })
      .addCase(signOut.rejected, (state) => {
        state.isSigningOut = false;
        // Clear state even on error
        state.isAuthenticated = false;
        state.user = null;
        state.sessionInfo.expiresAt = undefined;
        state.sessionInfo.roles = [];
      });

    // Refresh Token
    builder
      .addCase(refreshToken.pending, (state) => {
        state.isRefreshing = true;
      })
      .addCase(refreshToken.fulfilled, (state, action) => {
        state.isRefreshing = false;
        const result = action.payload;
        
        if (result.success && result.user) {
          state.isAuthenticated = true;
          state.user = result.user;
          state.sessionInfo.expiresAt = result.sessionInfo?.expiresAt;
          state.sessionInfo.roles = result.sessionInfo?.roles || [];
        }
      })
      .addCase(refreshToken.rejected, (state, action) => {
        state.isRefreshing = false;
        state.isAuthenticated = false;
        state.user = null;
        state.authError = action.payload as string;
      });
  },
  
});

// Export actions
export const {
  setAuthServiceInitialized,
  setInitError,
  clearAuthError,
  clearInitError,
  updateSessionInfo,
  setCheckingAuth,
} = authSlice.actions;

// Base selectors
const selectAuthSlice = (state: { auth: AuthState }) => state.auth;

// Memoized selectors using createSelector
export const selectAuthStatus = createSelector(
  [selectAuthSlice],
  (auth) => ({
    isAuthenticated: auth.isAuthenticated,
    isCheckingAuth: auth.isCheckingAuth,
    isInitialized: auth.isInitialized,
  })
);

export const selectUser = createSelector(
  [selectAuthSlice],
  (auth) => auth.user
);

export const selectLoadingStates = createSelector(
  [selectAuthSlice],
  (auth) => ({
    isSigningIn: auth.isSigningIn,
    isSigningUp: auth.isSigningUp,
    isSigningOut: auth.isSigningOut,
    isRefreshing: auth.isRefreshing,
    isCheckingAuth: auth.isCheckingAuth,
  })
);

export const selectErrors = createSelector(
  [selectAuthSlice],
  (auth) => ({
    authError: auth.authError,
    initError: auth.initError,
  })
);

export const selectSessionInfo = createSelector(
  [selectAuthSlice],
  (auth) => auth.sessionInfo
);

export const selectConfig = createSelector(
  [selectAuthSlice],
  (auth) => auth.config
);

// Computed selectors
export const selectHasRole = createSelector(
  [selectSessionInfo],
  (sessionInfo) => (role: string) => sessionInfo.roles.includes(role)
);

export const selectIsExpired = createSelector(
  [selectSessionInfo],
  (sessionInfo) => {
    if (!sessionInfo.expiresAt) return false;
    return new Date(sessionInfo.expiresAt) < new Date();
  }
);

// Export reducer
export default authSlice.reducer; 