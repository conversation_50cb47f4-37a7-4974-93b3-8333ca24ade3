"use client";

import React from 'react';
import { useAuth } from '@/hooks/features';
import { useAuth as useAuthContext } from '@/auth/providers/AuthServiceProvider';

export default function AuthTestReduxPage() {
  // New Redux-based auth
  const authRedux = useAuth();
  
  // Old Context-based auth (should now use Redux state internally)
  const authContext = useAuthContext();

  return (
    <div className="min-h-screen bg-gray-100 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Auth State Migration Test
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Redux Auth State */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 text-green-600">
              Redux Auth State (New)
            </h2>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="font-medium">Initialized:</span>
                <span className={authRedux.isInitialized ? 'text-green-600' : 'text-red-600'}>
                  {authRedux.isInitialized ? '✅' : '❌'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="font-medium">Authenticated:</span>
                <span className={authRedux.isAuthenticated ? 'text-green-600' : 'text-red-600'}>
                  {authRedux.isAuthenticated ? '✅' : '❌'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="font-medium">Checking Auth:</span>
                <span className={authRedux.isCheckingAuth ? 'text-yellow-600' : 'text-gray-600'}>
                  {authRedux.isCheckingAuth ? '🔄' : '✅'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="font-medium">User:</span>
                <span className="text-gray-600">
                  {authRedux.user ? (authRedux.user.name || authRedux.user.id) : 'null'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="font-medium">Auth Error:</span>
                <span className="text-red-600 text-sm">
                  {authRedux.authError || 'none'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="font-medium">Init Error:</span>
                <span className="text-red-600 text-sm">
                  {authRedux.initError || 'none'}
                </span>
              </div>
            </div>
          </div>

          {/* Context Auth State (Should now use Redux internally) */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 text-blue-600">
              Context Auth Hook (Should use Redux internally)
            </h2>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="font-medium">Initialized:</span>
                <span className={authContext.isInitialized ? 'text-green-600' : 'text-red-600'}>
                  {authContext.isInitialized ? '✅' : '❌'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="font-medium">Authenticated:</span>
                <span className={authContext.isAuthenticated ? 'text-green-600' : 'text-red-600'}>
                  {authContext.isAuthenticated ? '✅' : '❌'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="font-medium">Checking Auth:</span>
                <span className={authContext.isCheckingAuth ? 'text-yellow-600' : 'text-gray-600'}>
                  {authContext.isCheckingAuth ? '🔄' : '✅'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="font-medium">User:</span>
                <span className="text-gray-600">
                  {authContext.user ? (authContext.user.name || authContext.user.id) : 'null'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="font-medium">Auth Error:</span>
                <span className="text-red-600 text-sm">
                  {authContext.authError || 'none'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Test Actions */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Test Actions
          </h2>
          
          <div className="space-x-4">
            <button
              onClick={() => authContext.checkAuthStatus()}
              disabled={authContext.isCheckingAuth}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
            >
              {authContext.isCheckingAuth ? 'Checking...' : 'Check Auth Status'}
            </button>
            
            <button
              onClick={() => authContext.signOut()}
              disabled={!authContext.isAuthenticated}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
            >
              Sign Out
            </button>
          </div>
        </div>

        {/* State Sync Verification */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            State Synchronization Check
          </h2>
          
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>isAuthenticated sync:</span>
              <span className={authRedux.isAuthenticated === authContext.isAuthenticated ? 'text-green-600' : 'text-red-600'}>
                {authRedux.isAuthenticated === authContext.isAuthenticated ? '✅ Synced' : '❌ Out of sync'}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span>isCheckingAuth sync:</span>
              <span className={authRedux.isCheckingAuth === authContext.isCheckingAuth ? 'text-green-600' : 'text-red-600'}>
                {authRedux.isCheckingAuth === authContext.isCheckingAuth ? '✅ Synced' : '❌ Out of sync'}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span>user sync:</span>
              <span className={authRedux.user?.id === authContext.user?.id ? 'text-green-600' : 'text-red-600'}>
                {authRedux.user?.id === authContext.user?.id ? '✅ Synced' : '❌ Out of sync'}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 