/**
 * Auth Service Layer - Main Export
 *
 * This file exports all components of the auth service layer
 * for easy importing throughout the application.
 *
 * The service layer is designed to be transport-agnostic,
 * making it easy to switch between different auth providers
 * (SuperTokens → KeyCloak → Auth0) without changing UI code.
 */

// Core Interfaces
export type {
  IAuthService,
  IAuthServiceFactory,
  AuthConfig,
  AuthResult,
  AuthError,
  User,
  SessionInfo,
  DeviceInfo,
  SecurityMetadata,
  SignInInput,
  SignUpInput,
} from "./interfaces/IAuthService";

export type {
  ITransportAdapter,
  IGraphQLTransportAdapter,
  IRestTransportAdapter,
  ITransportAdapterFactory,
  ICookieManager,
  TransportResponse,
  TransportHealth,
  GraphQLOperation,
  RequestConfig,
} from "./interfaces/ITransportAdapter";

// Transport Adapters
export { GraphQLTransportAdapter } from "./adapters/GraphQLTransportAdapter";

// Service Implementations
export { GraphQLAuthService } from "./services/GraphQLAuthService";

// React Providers and Hooks
export {
  AuthServiceProvider,
  useAuthService,
  useAuthServiceState,
  useAuthConfig,
  withAuthService,
  AuthServiceDevTools,
} from "./providers/AuthServiceProvider";

// Configuration
export {
  createAuthConfig,
  getConfigForEnvironment,
  validateAuthConfig,
  defaultAuthConfig,
  authConfigHelpers,
} from "./config/authConfig";

export type {
  Environment,
  AuthProvider,
  ExtendedAuthConfig,
} from "./config/authConfig";

// Re-export for convenience
export const AUTH_PROVIDERS = {
  SUPERTOKENS: "supertokens" as const,
  KEYCLOAK: "keycloak" as const,
  AUTH0: "auth0" as const,
  CUSTOM: "custom" as const,
} as const;

export const ENVIRONMENTS = {
  DEVELOPMENT: "development" as const,
  STAGING: "staging" as const,
  PRODUCTION: "production" as const,
  TEST: "test" as const,
} as const;
